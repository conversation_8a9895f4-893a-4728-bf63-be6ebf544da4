# 抖店飞鸽AI自动聊天监控脚本

## 项目简介

这是一个基于Tampermonkey的用户脚本，专门为抖店飞鸽客服系统设计的AI自动聊天监控工具。该脚本能够实时监控抖店飞鸽聊天，自动回复客户消息，支持分类组管理、关键词回复、多媒体消息、AI智能回复等功能。

## 主要功能

### 🤖 智能回复系统
- **关键词自动回复**：基于预设关键词自动回复客户消息
- **AI上下文回复**：当无匹配关键词时，使用AI模型结合用户聊天记录和订单信息生成智能回复
- **分类组管理**：支持多个产品分类组，每个分类组可配置独立的关键词和回复内容
- **多媒体支持**：支持文本、图片、视频等多种消息类型的自动回复

### 📊 实时监控
- **聊天状态监控**：实时监控待回复、已回复、超时等不同状态的用户
- **订单信息获取**：自动获取用户180天内的订单信息
- **聊天记录跟踪**：实时获取用户与客服的完整聊天记录

### 🎯 特殊功能
- **特殊触发按钮**：支持在回复中添加特殊触发词，自动点击相应的"发送"按钮
  - `[订单商品规格]` - 点击第1个发送按钮
  - `[核对收货信息]` - 点击第2个发送按钮  
  - `[承诺发货时间]` - 点击第3个发送按钮
- **配置导出导入**：支持配置的导出和导入，便于备份和迁移
- **媒体文件管理**：使用IndexedDB存储大型媒体文件，支持图片和视频

## 技术特点

### 🏗️ 架构设计
- **模块化设计**：代码按功能模块组织，便于维护和扩展
- **事件驱动**：使用MutationObserver监控DOM变化，实时响应页面状态
- **异步处理**：采用Promise和async/await处理异步操作
- **错误处理**：完善的错误处理机制，确保脚本稳定运行

### 💾 数据存储
- **分块存储**：大型配置数据采用分块存储，突破浏览器存储限制
- **IndexedDB**：媒体文件使用IndexedDB存储，支持大容量数据
- **内存缓存**：提供内存缓存机制，提高数据访问速度
- **备份机制**：支持配置的导出导入，防止数据丢失

### 🚀 混合策略优先级处理
- **智能调度**：解决白名单与非白名单用户处理冲突
- **优先级管理**：白名单用户始终优先处理，永不阻塞
- **时间阈值控制**：可配置手动处理时间阈值（10-300秒）
- **自动抢占机制**：超时后白名单用户可抢占处理
- **队列管理**：自动管理用户处理队列，智能调度
- **状态恢复**：手动回复完成后自动继续处理队列
- **无缝体验**：解决死锁问题，确保流畅的客服体验

### 🔧 用户界面
- **可视化配置**：提供友好的配置界面，支持拖拽排序
- **实时预览**：配置修改实时生效，无需重启脚本
- **状态提示**：实时显示脚本运行状态和操作结果
- **折叠面板**：支持界面元素的折叠展开，节省空间

## 安装和使用

### 前置要求
1. **浏览器**：Chrome、Firefox、Edge等现代浏览器
2. **Tampermonkey扩展**：需要安装Tampermonkey浏览器扩展
3. **抖店飞鸽权限**：需要有抖店飞鸽客服系统的访问权限

### 安装步骤

#### 1. 安装Tampermonkey扩展
- **Chrome浏览器**：
  1. 打开Chrome网上应用店
  2. 搜索"Tampermonkey"
  3. 点击"添加至Chrome"安装

- **Firefox浏览器**：
  1. 打开Firefox附加组件商店
  2. 搜索"Tampermonkey"
  3. 点击"添加到Firefox"安装

- **Edge浏览器**：
  1. 打开Microsoft Edge加载项商店
  2. 搜索"Tampermonkey"
  3. 点击"获取"安装

#### 2. 安装脚本
1. 点击Tampermonkey扩展图标
2. 选择"管理面板"
3. 点击"新建脚本"
4. 删除默认内容，复制粘贴`Tampermonkey.js`文件的完整内容
5. 按`Ctrl+S`保存脚本

#### 3. 配置脚本
1. 打开抖店飞鸽客服系统：`https://im.jinritemai.com/pc_seller_v2/main/workspace`
2. 页面底部会出现"AI配置"按钮
3. 点击"AI配置"按钮打开配置面板
4. 根据需要配置各项参数

### 基础配置

#### 1. 分类组配置
1. 在配置面板中可以看到默认的分类组（通用、五代耳机、头戴式耳机等）
2. 点击分类组按钮切换到对应分类
3. 右键点击分类组按钮可以编辑分类组信息
4. 点击分类组右上角的"×"可以删除分类组
5. 拖拽分类组按钮可以调整排序

#### 2. 关键词配置
1. 选择要配置的分类组
2. 点击"添加关键词"按钮
3. 填写触发关键词和回复内容
4. 支持添加图片和视频作为回复内容
5. 可以设置多个关键词对应同一个回复

#### 3. AI接口配置
1. 在配置面板中找到"AI接口配置"部分
2. 填写以下信息：
   - **API地址**：AI服务的API地址
   - **应用ID**：AI应用的唯一标识
   - **授权密钥**：API访问授权密钥
3. 勾选"启用AI智能回复"
4. 设置默认回复内容（当AI接口不可用时使用）

### 高级配置

#### 1. 特殊触发词配置
脚本内置了三个特殊触发词，可以在回复内容中使用：
- `[订单商品规格]`：自动点击订单区域第1个"发送"按钮
- `[核对收货信息]`：自动点击订单区域第2个"发送"按钮
- `[承诺发货时间]`：自动点击订单区域第3个"发送"按钮

使用方法：在关键词回复内容中添加这些触发词，脚本会在发送消息后自动点击对应按钮。

#### 2. 触发词显示设置
在AI接口配置中有"发送触发词给用户"选项：
- **勾选**：触发词会显示在发送给用户的消息中
- **不勾选**：触发词仅用于内部处理，不会显示给用户

#### 3. 媒体文件管理
- 脚本使用IndexedDB存储图片和视频文件
- 支持拖拽上传媒体文件
- 自动压缩和优化媒体文件大小
- 提供媒体文件的预览和管理功能

### 配置导出导入

#### 导出配置
1. 在配置面板底部找到"导出配置"按钮
2. 点击后会自动下载配置文件（JSON格式）
3. 配置文件包含所有分类组、关键词、AI设置等信息

#### 导入配置
1. 点击"导入配置"按钮
2. 选择之前导出的配置文件
3. 确认导入后配置会立即生效
4. 建议导入前先导出当前配置作为备份

## 运行机制

### 1. 页面监控
脚本启动后会：
1. 等待抖店飞鸽页面完全加载
2. 定位聊天列表容器并开始监控
3. 使用MutationObserver监听DOM变化
4. 实时更新用户状态和消息

### 2. 自动回复流程
1. **状态检测**：检测用户是否处于待回复状态
2. **消息获取**：获取用户最新消息内容
3. **关键词匹配**：在当前分类组中查找匹配的关键词
4. **回复生成**：
   - 有匹配关键词：使用预设回复内容
   - 无匹配关键词：调用AI接口生成智能回复
5. **消息发送**：自动发送回复消息
6. **特殊处理**：检查并执行特殊触发词操作

### 3. AI智能回复
当启用AI功能且无匹配关键词时：
1. 收集用户聊天记录和订单信息
2. 构建包含上下文的提示词
3. 调用AI接口获取智能回复
4. 处理AI回复并发送给用户

## 故障排除

### 常见问题

#### 1. 脚本无法启动
**症状**：页面没有出现"AI配置"按钮
**解决方案**：
- 确认Tampermonkey扩展已正确安装并启用
- 检查脚本是否已保存并启用
- 刷新抖店飞鸽页面
- 检查浏览器控制台是否有错误信息

#### 2. 无法自动回复
**症状**：脚本运行但不自动回复消息
**解决方案**：
- 检查用户是否处于"待回复"状态
- 确认关键词配置是否正确
- 检查AI接口配置是否完整
- 查看浏览器控制台的日志信息

#### 3. 媒体文件无法发送
**症状**：图片或视频回复失败
**解决方案**：
- 检查媒体文件大小是否过大
- 确认浏览器支持IndexedDB
- 清除浏览器缓存后重试
- 检查网络连接是否稳定

#### 4. AI接口调用失败
**症状**：AI智能回复不工作
**解决方案**：
- 验证API地址、应用ID、授权密钥是否正确
- 检查AI服务是否正常运行
- 确认网络可以访问AI接口
- 查看控制台的API错误信息

### 调试方法

#### 1. 控制台日志
脚本会在浏览器控制台输出详细的运行日志：
1. 按F12打开开发者工具
2. 切换到"Console"标签
3. 查看以"[AI监控]"开头的日志信息
4. 根据日志信息定位问题

#### 2. 状态提示
脚本会在页面右上角显示状态提示：
- 绿色：正常运行状态
- 蓝色：加载或处理中
- 橙色：警告信息
- 红色：错误状态

#### 3. 配置检查
在配置面板中检查：
- 分类组和关键词是否正确配置
- AI接口参数是否完整
- 默认回复内容是否设置

## 注意事项

### 1. 使用限制
- 脚本仅在抖店飞鸽客服系统中有效
- 需要有相应的客服权限才能正常使用
- 建议在测试环境中先验证配置

### 2. 性能考虑
- 大量媒体文件可能影响浏览器性能
- 建议定期清理不需要的配置和媒体文件
- 避免设置过于复杂的关键词规则

### 3. 安全提醒
- 不要在公共电脑上保存敏感的API密钥
- 定期备份重要的配置数据
- 注意保护客户隐私信息

### 4. 更新维护
- 定期检查脚本是否有更新版本
- 抖店飞鸽系统更新可能需要调整脚本
- 建议关注脚本的兼容性问题

## 技术支持

如果在使用过程中遇到问题，可以：
1. 查看浏览器控制台的错误信息
2. 检查脚本配置是否正确
3. 尝试重新安装或更新脚本
4. 联系技术支持获取帮助

## 项目结构

```
抖店飞鸽AI自动聊天监控/
├── Tampermonkey.js          # 主脚本文件
├── README.md               # 项目说明文档
└── 配置示例/
    ├── 基础配置.json        # 基础配置示例
    ├── 高级配置.json        # 高级配置示例
    └── 媒体文件示例/        # 媒体文件示例
```

## 代码结构说明

### 核心模块

#### 1. 配置管理模块 (Lines 315-535)
- **功能**：管理脚本的所有配置信息
- **特点**：支持分块存储，突破浏览器存储限制
- **主要函数**：
  - `saveConfigToStorage()` - 保存配置到本地存储
  - `loadConfigFromStorage()` - 从本地存储加载配置
  - `clearStoredConfig()` - 清理存储的配置数据

#### 2. 媒体文件管理模块 (Lines 54-310)
- **功能**：处理图片、视频等媒体文件的存储和读取
- **技术**：使用IndexedDB存储大型媒体文件
- **主要函数**：
  - `initMediaDB()` - 初始化媒体数据库
  - `saveMediaToDB()` - 保存媒体文件到数据库
  - `loadMediaFromDB()` - 从数据库加载媒体文件
  - `deleteMediaFromDB()` - 删除媒体文件

#### 3. 聊天监控模块 (Lines 673-1082)
- **功能**：实时监控聊天列表和消息状态
- **技术**：使用MutationObserver监听DOM变化
- **主要函数**：
  - `processChatList()` - 处理聊天列表数据
  - `observeOrderInfo()` - 监控订单信息变化
  - `observeChatMessages()` - 监控聊天消息变化
  - `fetchChatMessages()` - 获取聊天消息内容

#### 4. 自动回复模块 (Lines 2600-2810)
- **功能**：根据关键词或AI智能生成回复
- **特点**：支持多媒体回复和特殊触发词
- **主要函数**：
  - `autoReplyToWaitReplyUsers()` - 自动回复待回复用户
  - `findMatchingKeyword()` - 查找匹配的关键词
  - `sendMessageToUser()` - 发送消息给用户

#### 5. AI接口模块 (Lines 3081-3163)
- **功能**：与AI服务进行交互，获取智能回复
- **协议**：支持标准的HTTP API调用
- **主要函数**：
  - `getAIResponse()` - 获取AI智能回复
  - 支持上下文理解和订单信息整合

#### 6. 用户界面模块 (Lines 1088-2599)
- **功能**：提供可视化的配置界面
- **特点**：支持拖拽排序、实时预览
- **主要组件**：
  - 分类组管理界面
  - 关键词配置界面
  - AI接口配置界面
  - 配置导出导入界面

### 特殊功能实现

#### 1. 特殊触发词系统 (Lines 21-25, 2882-2902)
```javascript
const specialSendTriggers = [
    { triggerText: "[订单商品规格]", btnIndex: 0 },
    { triggerText: "[核对收货信息]", btnIndex: 1 },
    { triggerText: "[承诺发货时间]", btnIndex: 2 }
];
```
- 在回复内容中检测特殊触发词
- 自动点击对应的订单操作按钮
- 支持自定义触发词和按钮映射

#### 2. 分块存储系统 (Lines 335-485)
- 解决浏览器存储大小限制问题
- 自动将大型配置分块存储
- 支持完整性校验和错误恢复

#### 3. 媒体文件处理 (Lines 2905-3076)
- 支持Base64编码的图片和视频
- 使用拖拽事件模拟文件上传
- 批量处理多个媒体文件

## 配置文件格式

### 基础配置结构
```json
{
  "categories": [
    {
      "name": "分类组名称",
      "keyword": "分类关键词",
      "order": 0,
      "isDefault": true,
      "keywords": [
        {
          "keyword": "触发关键词",
          "reply": "回复内容",
          "images": ["图片ID1", "图片ID2"],
          "videos": ["视频ID1"]
        }
      ]
    }
  ],
  "fallbackReply": "默认回复内容",
  "apiURL": "AI接口地址",
  "applicationID": "应用ID",
  "appAuthorization": "授权密钥",
  "useAIFallback": false,
  "sendTriggerWordsToUser": false
}
```

### 媒体文件存储格式
```javascript
{
  id: "media_timestamp_randomstring",
  data: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
  type: "image/png",
  timestamp: 1640995200000
}
```

## API接口规范

### AI接口调用流程

#### 1. 获取Chat ID
```http
GET /application/{applicationID}/chat/open
Headers:
  AUTHORIZATION: {appAuthorization}
  Accept: application/json

Response:
{
  "code": 200,
  "data": "chat_id_string"
}
```

#### 2. 发送消息获取回复
```http
POST /application/chat_message/{chat_id}
Headers:
  AUTHORIZATION: {appAuthorization}
  Accept: application/json
  Content-Type: application/json

Body:
{
  "message": "用户消息和上下文",
  "re_chat": false,
  "stream": false
}

Response:
{
  "code": 200,
  "data": {
    "content": "AI回复内容"
  }
}
```

### 提示词模板
脚本使用以下提示词模板与AI交互：
```
以下是用户的订单信息：
{订单信息}

用户最新的问题是："{用户消息}"

你是抖店客服AI聊天助手，是一个抖店客服智能体，负责为用户提供服务，解答关于商品信息、订单状态、修改地址等问题，因为所有的订单都是用户在抖音超级福袋活动中奖所得，都是0元支付免费赠送给用户的，是不支持取消、退货、退款、换货的，所以绝不能提及退款、退货、换货话术，用户兑奖后订单就无法取消的，并且用户兑奖后订单就会显示出来，所以奖品会在48小时内给用户安排发出，并且也不支持折现。
```

## 开发和定制

### 自定义特殊触发词
如需添加新的特殊触发词，修改以下代码：
```javascript
const specialSendTriggers = [
    { triggerText: "[订单商品规格]", btnIndex: 0 },
    { triggerText: "[核对收货信息]", btnIndex: 1 },
    { triggerText: "[承诺发货时间]", btnIndex: 2 },
    // 添加新的触发词
    { triggerText: "[自定义触发词]", btnIndex: 3 }
];
```

### 修改默认分类组
在代码中找到`defaultCategories`数组，可以修改默认的分类组配置：
```javascript
const defaultCategories = [
    { name: "通用", keyword: "", order: 0, isDefault: true, keywords: [] },
    { name: "自定义分类", keyword: "关键词", order: 1, keywords: [] }
    // 添加更多分类组
];
```

### 自定义AI提示词
修改`getAIResponse`函数中的`prompt`变量来自定义AI提示词：
```javascript
const prompt = `
自定义的提示词模板
用户订单信息：${orderText}
用户问题：${userMessage}
`;
```

## 性能优化建议

### 1. 媒体文件优化
- 图片建议压缩到500KB以下
- 视频建议压缩到2MB以下
- 定期清理不使用的媒体文件

### 2. 配置优化
- 避免设置过多的关键词规则
- 合理设置分类组数量（建议不超过20个）
- 定期导出配置进行备份

### 3. 监控优化
- 脚本会自动优化DOM监控频率
- 避免在高峰期进行大量配置修改
- 建议在低峰期测试新配置

## 更新日志

### v1.65 (当前版本)
- 新增特殊触发词功能
- 优化媒体文件存储机制
- 改进AI接口调用逻辑
- 增强配置导出导入功能
- 修复已知bug和性能问题

### 未来版本计划
- 支持更多AI接口协议
- 增加统计和分析功能
- 优化用户界面体验
- 支持多语言配置

---

**版本信息**：v1.65
**更新日期**：2024年
**兼容性**：Chrome、Firefox、Edge等现代浏览器
**依赖**：Tampermonkey扩展
**许可证**：仅供学习和研究使用
