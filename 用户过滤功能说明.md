# 用户过滤功能使用说明

## 🎯 功能概述

"判断用户"功能允许您精确控制哪些用户可以接收默认回复。启用此功能后，只有在用户列表中的用户才会收到默认回复，其他用户将被忽略，不会触发任何自动回复。

## ✨ 主要特性

### 1. **精确用户控制**
- 白名单机制：只有列表中的用户才会收到默认回复
- 支持精确匹配和模糊匹配
- 动态添加/删除用户，实时生效

### 2. **智能匹配算法**
- **精确匹配**：用户名完全相同
- **模糊匹配**：用户名包含关系（双向匹配）
- **灵活配置**：支持昵称、部分用户名等多种匹配方式

### 3. **不影响关键词回复**
- 用户过滤仅影响默认回复
- 关键词匹配的自动回复不受影响
- AI智能回复的fallback部分受影响

## 🔧 使用方法

### 1. **启用功能**
1. 在抖店飞鸽页面点击"AI配置"按钮
2. 在"默认回复"区域找到"判断用户"复选框
3. ✅ 勾选"判断用户"复选框
4. 系统显示"已启用用户过滤功能"提示
5. 用户列表管理区域自动显示

### 2. **添加用户**
1. 在"允许默认回复的用户列表"区域
2. 在输入框中输入用户名（支持模糊匹配）
3. 点击"+ 添加用户"按钮或按Enter键
4. 用户成功添加到列表中

### 3. **删除用户**
1. 在用户列表中找到要删除的用户
2. 点击该用户右侧的"删除"按钮
3. 用户从列表中移除

### 4. **关闭功能**
1. 取消勾选"判断用户"复选框
2. 用户列表区域自动隐藏
3. 所有用户恢复正常的默认回复

## 🎯 工作原理

### 1. **过滤逻辑**
```
用户发送消息 → 检查是否有关键词匹配 → 如果没有关键词匹配 → 检查用户过滤 → 决定是否发送默认回复
```

### 2. **匹配规则**
- **精确匹配**：`用户名 === 列表中的用户名`
- **模糊匹配**：`用户名.includes(列表用户名) || 列表用户名.includes(用户名)`

### 3. **优先级说明**
1. **关键词回复** - 最高优先级，不受用户过滤影响
2. **AI智能回复** - 中等优先级，fallback部分受用户过滤影响
3. **默认回复** - 最低优先级，完全受用户过滤控制

## 📋 配置示例

### 基础配置
```json
{
  "enableUserFilter": true,
  "allowedUsers": [
    "VIP客户",
    "老客户", 
    "张三",
    "李四"
  ],
  "fallbackReplies": [
    "您好，请问有什么可以帮助您的？",
    "您好，欢迎咨询！"
  ],
  "useRandomFallback": true
}
```

### 完整配置示例
```json
{
  "enableUserFilter": true,
  "allowedUsers": [
    "VIP客户",
    "老客户",
    "重要客户",
    "张三",
    "李四",
    "王五",
    "测试用户"
  ],
  "fallbackReplies": [
    "您好，感谢您的咨询！请问有什么可以帮助您的？",
    "您好，欢迎来到我们的店铺！请问需要什么帮助吗？",
    "您好，我是客服小助手，很高兴为您服务！"
  ],
  "useRandomFallback": true,
  "autoCloseSession": true
}
```

## 🎯 使用场景

### 1. **VIP客户专享服务**
- 只对VIP客户提供自动回复
- 普通客户需要人工处理
- 提升VIP客户体验

**配置示例：**
```json
{
  "enableUserFilter": true,
  "allowedUsers": ["VIP", "钻石会员", "黄金会员"]
}
```

### 2. **测试环境控制**
- 只对测试账号启用自动回复
- 避免影响真实客户
- 便于功能测试

**配置示例：**
```json
{
  "enableUserFilter": true,
  "allowedUsers": ["测试", "test", "demo"]
}
```

### 3. **特定客户群体**
- 针对特定客户群体的专项服务
- 老客户回访活动
- 特殊促销活动

**配置示例：**
```json
{
  "enableUserFilter": true,
  "allowedUsers": ["老客户", "回头客", "忠实粉丝"]
}
```

### 4. **客服分工协作**
- 不同客服负责不同客户群体
- 避免重复回复
- 提高服务效率

**配置示例：**
```json
{
  "enableUserFilter": true,
  "allowedUsers": ["A组客户", "B组客户", "新客户"]
}
```

## 🔍 匹配示例

### 1. **精确匹配**
- 列表中有："张三"
- 用户名："张三" ✅ 匹配
- 用户名："张三丰" ❌ 不匹配

### 2. **模糊匹配**
- 列表中有："VIP"
- 用户名："VIP客户" ✅ 匹配
- 用户名："超级VIP" ✅ 匹配
- 用户名："会员VIP用户" ✅ 匹配

### 3. **双向模糊匹配**
- 列表中有："老客户"
- 用户名："老客户张三" ✅ 匹配
- 用户名："张三老客户" ✅ 匹配

- 列表中有："张三老客户"
- 用户名："张三" ✅ 匹配
- 用户名："老客户" ✅ 匹配

## ⚙️ 高级设置

### 1. **批量导入用户**
可以通过配置文件批量导入用户列表：
```json
{
  "allowedUsers": [
    "用户1", "用户2", "用户3",
    "VIP客户", "老客户", "重要客户"
  ]
}
```

### 2. **用户名规则建议**
- 使用有意义的标识：如"VIP"、"老客户"
- 避免过于具体的用户名，增加匹配灵活性
- 可以使用分类标签：如"A组"、"B组"

### 3. **性能优化**
- 用户列表建议控制在100个以内
- 避免使用过长的用户名
- 定期清理无效用户

## 🚨 注意事项

### 1. **功能限制**
- ⚠️ 仅影响默认回复，不影响关键词回复
- ⚠️ 用户列表为空时，所有用户都不会收到默认回复
- ⚠️ 关闭功能后，所有用户恢复正常回复

### 2. **匹配规则**
- ✅ 支持中文、英文、数字、符号
- ✅ 大小写敏感
- ✅ 支持特殊字符

### 3. **性能考虑**
- ✅ 匹配算法高效，不影响回复速度
- ✅ 用户列表实时更新，无需重启
- ✅ 配置自动保存，重新加载后保持设置

## 🔍 故障排除

### 1. **用户没有收到默认回复**
**可能原因：**
- 用户过滤功能已启用
- 用户不在允许列表中
- 用户名匹配规则不符合

**解决方法：**
- 检查"判断用户"是否勾选
- 确认用户名是否在列表中
- 尝试添加更宽泛的匹配规则

### 2. **不应该回复的用户收到了回复**
**可能原因：**
- 触发了关键词回复
- 用户名意外匹配了列表中的项目
- 用户过滤功能未启用

**解决方法：**
- 检查是否有关键词匹配
- 审查用户列表中的匹配规则
- 确认用户过滤功能已启用

### 3. **匹配不准确**
**可能原因：**
- 模糊匹配规则过于宽泛
- 用户名包含特殊字符
- 匹配逻辑理解有误

**解决方法：**
- 使用更精确的用户名
- 测试不同的匹配规则
- 查看控制台日志了解匹配过程

## 📊 效果监控

### 1. **日志查看**
在浏览器控制台（F12）中可以看到过滤日志：
- `[用户过滤] 用户 xxx 不在允许列表中，跳过默认回复`
- `[AI监控] 开始处理用户: xxx`

### 2. **效果评估**
- 观察哪些用户收到了默认回复
- 检查是否符合预期的过滤规则
- 统计过滤效果和客服工作量变化

### 3. **优化建议**
- 根据实际使用情况调整用户列表
- 优化匹配规则提高准确性
- 定期审查和更新用户列表

## 🎉 最佳实践

### 1. **合理设置用户列表**
- 根据业务需求设置用户分类
- 使用有意义的标识符
- 定期维护和更新列表

### 2. **配合其他功能使用**
- 与关键词回复配合，提供完整的自动回复体系
- 与随机默认回复配合，增加回复多样性
- 与自动关闭会话配合，提高处理效率

### 3. **渐进式启用**
- 先在测试环境验证功能
- 从小范围用户开始启用
- 根据效果逐步扩大范围

### 4. **团队协作**
- 制定统一的用户分类标准
- 定期同步用户列表更新
- 建立用户过滤的使用规范

通过合理使用用户过滤功能，您可以实现精准的客户服务，提高工作效率，为不同类型的客户提供差异化的服务体验！
