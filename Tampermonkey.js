// ==UserScript==
// @name         抖店飞鸽AI自动聊天监控（上下文AI回复+分类组高级版）+特殊触发按钮
// @namespace    http://tampermonkey.net/
// @version      1.65
// @description  实时监控抖店飞鸽聊天，自动回复。支持分类组管理、关键词回复、多媒体、默认回复、AI接口配置、配置导出导入。无关键词时将用户+客服聊天记录与订单信息传给AI模型获取上下文智能回复。并且可以在关键词回复内容里添加特殊触发词([订单商品规格]、[核对收货信息]、[承诺发货时间]等)来点击相应"发送"按钮。
// @match        https://im.jinritemai.com/pc_seller_v2/main/workspace
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_xmlhttpRequest
// @grant        GM_deleteValue
// @grant        unsafeWindow
// ==/UserScript==

(function () {
    'use strict';

    /****************************************************************
     * 1. 新增：定义特殊触发词数组（文字 -> 点击哪个"发送"按钮）
     *    根据业务需求，可自行增减、调整触发词和按钮下标
     ****************************************************************/
    const specialSendTriggers = [
        { triggerText: "[订单商品规格]", btnIndex: 0 },
        { triggerText: "[核对收货信息]", btnIndex: 1 },
        { triggerText: "[承诺发货时间]", btnIndex: 2 }
    ];

    /************************************************************
     * 原有变量、默认分类组等
     ************************************************************/
    let isNoUser = false;
    let lastStatusMap = {};
    let lastSelectedUser = null;
    let lastOrderInfo = null;
    let lastChatMessages = [];
    let isOrderMonitoring = false;
    let repliedMessagesMap = {};

    // 默认分类组示例
    const defaultCategories = [
        { name: "通用", keyword: "", order: 0, isDefault: true, keywords: [] },
        { name: "五代耳机", keyword: "耳机", order: 1, keywords: [] },
        { name: "头戴式耳机", keyword: "头戴", order: 2, keywords: [] },
        { name: "吨吨杯", keyword: "杯", order: 3, keywords: [] },
        { name: "口红", keyword: "口红", order: 4, keywords: [] },
        { name: "手表", keyword: "手表", order: 5, keywords: [] },
        { name: "电饭煲", keyword: "电饭煲", order: 6, keywords: [] },
        { name: "银吊坠", keyword: "吊坠", order: 7, keywords: [] },
        { name: "扫地机", keyword: "扫地机", order: 8, keywords: [] },
        { name: "平板", keyword: "平板", order: 9, keywords: [] }
    ];

    /************************************************************
     * 媒体文件存储系统 - 使用IndexedDB代替GM_setValue存储大型媒体文件
     ************************************************************/
    const DB_NAME = 'FigeonMediaStore';
    const DB_VERSION = 2; // 更新版本号从1到2
    const MEDIA_STORE = 'mediaFiles';

    // 初始化数据库
    const initMediaDB = () => {
        return new Promise((resolve, reject) => {
            // 首先尝试获取当前数据库版本
            const checkRequest = indexedDB.open(DB_NAME);

            checkRequest.onsuccess = (event) => {
                const currentVersion = event.target.result.version;
                event.target.result.close();

                // 使用当前版本或DB_VERSION中较大的值打开数据库
                const useVersion = Math.max(currentVersion, DB_VERSION);
                const request = indexedDB.open(DB_NAME, useVersion);

                request.onerror = (event) => {
                    console.error('[AI监控] 打开IndexedDB失败:', event.target.error);
                    reject(event.target.error);
                };

                request.onupgradeneeded = (event) => {
                    const db = event.target.result;
                    // 创建媒体文件存储对象仓库
                    if (!db.objectStoreNames.contains(MEDIA_STORE)) {
                        db.createObjectStore(MEDIA_STORE, { keyPath: 'id' });
                        console.log('[AI监控] 媒体存储对象仓库创建成功');
                    }
                };

                request.onsuccess = (event) => {
                    const db = event.target.result;
                    console.log(`[AI监控] IndexedDB媒体存储连接成功 (版本: ${db.version})`);
                    resolve(db);
                };
            };

            checkRequest.onerror = (event) => {
                console.error('[AI监控] 检查IndexedDB版本失败:', event.target.error);

                // 如果检查失败，尝试直接用指定版本打开
                const request = indexedDB.open(DB_NAME, DB_VERSION);

                request.onerror = (event) => {
                    console.error('[AI监控] 打开IndexedDB失败:', event.target.error);
                    reject(event.target.error);
                };

                request.onupgradeneeded = (event) => {
                    const db = event.target.result;
                    if (!db.objectStoreNames.contains(MEDIA_STORE)) {
                        db.createObjectStore(MEDIA_STORE, { keyPath: 'id' });
                        console.log('[AI监控] 媒体存储对象仓库创建成功');
                    }
                };

                request.onsuccess = (event) => {
                    const db = event.target.result;
                    console.log(`[AI监控] IndexedDB媒体存储连接成功 (版本: ${db.version})`);
                    resolve(db);
                };
            };
        });
    };

    // 保存媒体文件到数据库，返回媒体ID
    const saveMediaToDB = (mediaData, mediaType) => {
        return new Promise((resolve, reject) => {
            // 生成唯一ID
            const mediaId = 'media_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

            // 尝试存储到IndexedDB
            initMediaDB().then(db => {
                const transaction = db.transaction([MEDIA_STORE], 'readwrite');
                const store = transaction.objectStore(MEDIA_STORE);

                // 存储媒体数据和类型
                const storeRequest = store.put({
                    id: mediaId,
                    data: mediaData,
                    type: mediaType,
                    timestamp: Date.now()
                });

                storeRequest.onsuccess = () => {
                    console.log(`[AI监控] 媒体文件保存成功，ID: ${mediaId}`);
                    resolve(mediaId);
                };

                storeRequest.onerror = (event) => {
                    console.error('[AI监控] 保存媒体文件失败:', event.target.error);
                    fallbackMediaStorage(mediaId, mediaData);
                    resolve(mediaId); // 继续使用ID，后续会从内存中读取
                };

                transaction.oncomplete = () => {
                    db.close();
                };
            }).catch(error => {
                console.error('[AI监控] 初始化媒体数据库失败:', error);
                // 使用备选存储方法
                fallbackMediaStorage(mediaId, mediaData);
                resolve(mediaId); // 继续使用ID，后续会从内存中读取
            });
        });
    };

    // 内存中的媒体文件备选存储
    const mediaMemoryStore = {};

    // 当IndexedDB不可用时，使用内存存储作为备选
    const fallbackMediaStorage = (mediaId, mediaData) => {
        mediaMemoryStore[mediaId] = mediaData;
        console.log(`[AI监控] 媒体文件已存储在内存中，ID: ${mediaId}`);
        // 显示警告信息
        showStatusMessage('媒体文件暂存在内存中。请注意，刷新页面后这些文件将丢失。', 'orange');
    };

    // 从数据库加载媒体文件
    const loadMediaFromDB = (mediaId) => {
        return new Promise((resolve, reject) => {
            if (!mediaId || !mediaId.startsWith('media_')) {
                // 如果是旧的完整base64数据，直接返回
                resolve(mediaId);
                return;
            }

            // 首先尝试从内存存储中读取
            if (mediaMemoryStore[mediaId]) {
                console.log(`[AI监控] 从内存中加载媒体文件，ID: ${mediaId}`);
                resolve(mediaMemoryStore[mediaId]);
                return;
            }

            // 从IndexedDB加载
            initMediaDB().then(db => {
                const transaction = db.transaction([MEDIA_STORE], 'readonly');
                const store = transaction.objectStore(MEDIA_STORE);
                const getRequest = store.get(mediaId);

                getRequest.onsuccess = (event) => {
                    const result = event.target.result;
                    if (result) {
                        console.log(`[AI监控] 加载媒体文件成功，ID: ${mediaId}`);
                        // 同时缓存到内存中以加快后续访问
                        mediaMemoryStore[mediaId] = result.data;
                        resolve(result.data);
                    } else {
                        console.warn(`[AI监控] 未找到ID为${mediaId}的媒体文件`);
                        resolve(null);
                    }
                };

                getRequest.onerror = (event) => {
                    console.error('[AI监控] 加载媒体文件失败:', event.target.error);
                    resolve(null); // 返回null而不是拒绝Promise
                };

                transaction.oncomplete = () => {
                    db.close();
                };
            }).catch(error => {
                console.error('[AI监控] 初始化媒体数据库失败:', error);
                resolve(null); // 返回null而不是拒绝Promise
            });
        });
    };

    // 批量加载媒体文件
    const loadMediaBatch = async (mediaIds) => {
        const results = [];
        for (const mediaId of mediaIds) {
            try {
                const data = await loadMediaFromDB(mediaId);
                results.push(data);
            } catch (error) {
                console.error(`[AI监控] 加载媒体ID ${mediaId} 失败:`, error);
                results.push(null);
            }
        }
        return results;
    };

    // 删除媒体文件
    const deleteMediaFromDB = (mediaId) => {
        return new Promise((resolve, reject) => {
            if (!mediaId || !mediaId.startsWith('media_')) {
                resolve(true); // 旧数据无需删除
                return;
            }

            // 删除内存中的缓存
            if (mediaMemoryStore[mediaId]) {
                delete mediaMemoryStore[mediaId];
                console.log(`[AI监控] 已从内存中删除媒体文件，ID: ${mediaId}`);
            }

            // 从IndexedDB中删除
            initMediaDB().then(db => {
                const transaction = db.transaction([MEDIA_STORE], 'readwrite');
                const store = transaction.objectStore(MEDIA_STORE);
                const deleteRequest = store.delete(mediaId);

                deleteRequest.onsuccess = () => {
                    console.log(`[AI监控] 删除媒体文件成功，ID: ${mediaId}`);
                    resolve(true);
                };

                deleteRequest.onerror = (event) => {
                    console.error('[AI监控] 删除媒体文件失败:', event.target.error);
                    // 即使IndexedDB删除失败，已从内存中删除，仍然视为成功
                    resolve(true);
                };

                transaction.oncomplete = () => {
                    db.close();
                };
            }).catch(error => {
                console.error('[AI监控] 初始化媒体数据库失败:', error);
                // 至少从内存中删除了，视为部分成功
                resolve(true);
            });
        });
    };

    // 初始化媒体数据库
    initMediaDB().catch(error => {
        console.error('[AI监控] 初始化媒体数据库失败:', error);
        handleDBInitFailure(error);
    });

    // 处理数据库初始化失败的情况
    const handleDBInitFailure = (error) => {
        // 添加一个提示，指导用户刷新页面或清除缓存
        const errorMessage = error.toString();
        if (errorMessage.includes('version')) {
            const messageDiv = document.createElement('div');
            messageDiv.style.cssText = 'position:fixed;top:10px;left:10px;background:red;color:white;padding:10px;z-index:9999;border-radius:5px;max-width:80%;';
            messageDiv.innerHTML = `
                <p><strong>[AI监控]</strong> IndexedDB版本不匹配，请尝试以下操作：</p>
                <ol>
                    <li>刷新页面（按F5键）</li>
                    <li>如仍有问题，请尝试清除浏览器缓存</li>
                    <li>或者，重新安装脚本最新版本</li>
                </ol>
                <button id="closeDbErrorMsg" style="background:white;color:red;border:none;padding:5px 10px;border-radius:3px;cursor:pointer;margin-top:5px;">关闭提示</button>
            `;
            document.body.appendChild(messageDiv);

            document.getElementById('closeDbErrorMsg').addEventListener('click', () => {
                messageDiv.remove();
            });
        }
    };

    /************************************************************
     * 在 aiConfig 里新增一个布尔开关：sendTriggerWordsToUser，
     * 用于控制"是否把触发词也一起发送给用户"（默认 false 不发送）
     ************************************************************/
    let aiConfig = {
        categories: defaultCategories,
        fallbackReply: "您好，请问有什么可以帮助您的？",
        fallbackReplies: ["您好，请问有什么可以帮助您的？"], // 新增：默认回复列表
        useRandomFallback: false, // 新增：是否使用随机默认回复
        autoCloseSession: false, // 新增：自动关闭会话
        enableUserFilter: false, // 新增：是否启用用户过滤
        allowedUsers: [], // 新增：允许默认回复的用户列表
        manualReplyTimeout: 30, // 新增：手动处理时间阈值（秒），超过此时间白名单用户可以抢占
        apiURL: "",
        applicationID: "",
        appAuthorization: "",
        useAIFallback: false,
        sendTriggerWordsToUser: false // 默认不发送触发词给用户
    };

    const statusesToAutoReply = [
        "message_group_name_waitReply",
        "message_group_name_autoReply",
        "message_group_name_overThreemins"
    ];

    /************************************************************
     * 配置分块存储与读取函数
     ************************************************************/
    const CONFIG_CHUNK_SIZE = 100000; // 每块大小限制约100KB
    const CONFIG_MAIN_KEY = "figeonAiConfig_main";

    // 保存配置到存储
    function saveConfigToStorage() {
        try {
            // 清理旧的分块存储
            clearStoredConfig();

            // 序列化配置
            const configStr = JSON.stringify(aiConfig);

            // 如果配置较小，直接存储
            if (configStr.length < CONFIG_CHUNK_SIZE) {
                GM_setValue(CONFIG_MAIN_KEY, configStr);
                return;
            }

            // 分块存储
            const chunks = Math.ceil(configStr.length / CONFIG_CHUNK_SIZE);

            // 存储主信息
            const mainInfo = {
                chunks: chunks,
                totalSize: configStr.length,
                lastSaved: new Date().getTime()
            };
            GM_setValue(CONFIG_MAIN_KEY, JSON.stringify(mainInfo));

            // 分块存储主体
            for (let i = 0; i < chunks; i++) {
                const start = i * CONFIG_CHUNK_SIZE;
                const end = Math.min(start + CONFIG_CHUNK_SIZE, configStr.length);
                const chunk = configStr.substring(start, end);
                GM_setValue(`figeonAiConfig_chunk_${i}`, chunk);
            }

            console.log(`[AI监控] 配置已分${chunks}块保存，总大小: ${(configStr.length/1024).toFixed(2)}KB`);
        } catch (error) {
            console.error("[AI监控] 保存配置失败:", error);
        }
    }

    // 从存储加载配置
    function loadConfigFromStorage() {
        try {
            // 尝试获取主信息
            const mainData = GM_getValue(CONFIG_MAIN_KEY);
            if (!mainData) {
                console.log("[AI监控] 未找到配置数据");
                return false;
            }

            // 检查是否是直接存储的配置
            try {
                const parsed = JSON.parse(mainData);
                if (parsed && typeof parsed === 'object') {
                    // 检查是否是主信息对象
                    if (parsed.chunks && parsed.totalSize) {
                        // 这是分块存储信息，需要合并块
                        let configStr = "";
                        for (let i = 0; i < parsed.chunks; i++) {
                            const chunkKey = `figeonAiConfig_chunk_${i}`;
                            const chunk = GM_getValue(chunkKey);
                            if (!chunk) {
                                console.error(`[AI监控] 配置块 ${i} 丢失`);
                                return false;
                            }
                            configStr += chunk;
                        }

                        // 检查大小是否匹配
                        if (configStr.length !== parsed.totalSize) {
                            console.warn(`[AI监控] 配置大小不匹配: 预期${parsed.totalSize}字节, 实际${configStr.length}字节`);
                        }

                        processLoadedConfig(configStr);
                    } else {
                        // 这是直接存储的配置
                        processLoadedConfig(mainData);
                    }
                    return true;
                }
            } catch (e) {
                console.error("[AI监控] 解析配置失败:", e);
                return false;
            }
        } catch (error) {
            console.error("[AI监控] 加载配置失败:", error);
            return false;
        }
    }

    // 处理加载的配置数据
    function processLoadedConfig(configStr) {
        try {
            const parsed = JSON.parse(configStr);
            if (parsed && typeof parsed === 'object') {
                aiConfig = parsed;
                // 如果 categories 不存在，就用默认
                if (!aiConfig.categories) {
                    const oldKeywords = aiConfig.keywords || [];
                    aiConfig.categories = JSON.parse(JSON.stringify(defaultCategories));
                    const commonCategory = aiConfig.categories.find(c => c.name === "通用");
                    if (commonCategory) {
                        commonCategory.keywords = oldKeywords;
                    }
                    delete aiConfig.keywords;
                }
                aiConfig.categories.forEach(cat => {
                    if (cat.order === undefined) cat.order = 999;
                    if (!cat.keywords) cat.keywords = [];
                });
                // 根据 order 排序分类组
                aiConfig.categories.sort((a, b) => a.order - b.order);

                // 如果没有 sendTriggerWordsToUser 字段，就补上
                if (typeof aiConfig.sendTriggerWordsToUser === 'undefined') {
                    aiConfig.sendTriggerWordsToUser = false;
                }

                console.log("[AI监控] 配置加载成功");
            }
        } catch (e) {
            console.error("[AI监控] 处理配置数据失败:", e);
        }
    }

    // 清理所有存储的配置块
    function clearStoredConfig() {
        try {
            const mainData = GM_getValue(CONFIG_MAIN_KEY);
            if (mainData) {
                try {
                    const parsed = JSON.parse(mainData);
                    if (parsed && parsed.chunks) {
                        for (let i = 0; i < parsed.chunks; i++) {
                            GM_deleteValue(`figeonAiConfig_chunk_${i}`);
                        }
                    }
                } catch (e) {
                    // 忽略解析错误
                }
            }

            // 兼容旧版本存储
            GM_deleteValue("figeonAiConfig");
        } catch (error) {
            console.error("[AI监控] 清理配置失败:", error);
        }
    }

    // 加载配置
    loadConfigFromStorage();

    /************************************************************
     * 读取配置（从 GM_value 中）
     ************************************************************/
    // 保留旧的加载逻辑作为备份，以防新方法失败
    (function loadConfigFromStorageOld() {
        const storedConfig = GM_getValue("figeonAiConfig");
        if (storedConfig && !loadConfigFromStorage()) {
            try {
                const parsed = JSON.parse(storedConfig);
                if (parsed && typeof parsed === 'object') {
                    aiConfig = parsed;
                    // 如果 categories 不存在，就用默认
                    if (!aiConfig.categories) {
                        const oldKeywords = aiConfig.keywords || [];
                        aiConfig.categories = JSON.parse(JSON.stringify(defaultCategories));
                        const commonCategory = aiConfig.categories.find(c => c.name === "通用");
                        if (commonCategory) {
                            commonCategory.keywords = oldKeywords;
                        }
                        delete aiConfig.keywords;
                    }
                    aiConfig.categories.forEach(cat => {
                        if (cat.order === undefined) cat.order = 999;
                        if (!cat.keywords) cat.keywords = [];
                    });
                    // 根据 order 排序分类组
                    aiConfig.categories.sort((a, b) => a.order - b.order);

                    // 如果没有 sendTriggerWordsToUser 字段，就补上
                    if (typeof aiConfig.sendTriggerWordsToUser === 'undefined') {
                        aiConfig.sendTriggerWordsToUser = false;
                    }

                    // 兼容旧版本：如果没有新的默认回复列表字段，就从旧字段迁移
                    if (!aiConfig.fallbackReplies) {
                        aiConfig.fallbackReplies = [aiConfig.fallbackReply || "您好，请问有什么可以帮助您的？"];
                    }
                    if (typeof aiConfig.useRandomFallback === 'undefined') {
                        aiConfig.useRandomFallback = false;
                    }
                    if (typeof aiConfig.autoCloseSession === 'undefined') {
                        aiConfig.autoCloseSession = false;
                    }
                    if (typeof aiConfig.enableUserFilter === 'undefined') {
                        aiConfig.enableUserFilter = false;
                    }
                    if (!aiConfig.allowedUsers) {
                        aiConfig.allowedUsers = [];
                    }
                }
            } catch (e) {
                aiConfig = {
                    categories: defaultCategories,
                    fallbackReply: "您好，请问有什么可以帮助您的？",
                    fallbackReplies: ["您好，请问有什么可以帮助您的？"],
                    useRandomFallback: false,
                    autoCloseSession: false,
                    enableUserFilter: false,
                    allowedUsers: [],
                    apiURL: "",
                    applicationID: "",
                    appAuthorization: "",
                    useAIFallback: false,
                    sendTriggerWordsToUser: false
                };
            }
        }
    })();

    /************************************************************
     * 日志工具
     ************************************************************/
    const aiLogger = {
        log: (...args) => console.log(...args),
        warn: (...args) => console.warn(...args),
        error: (...args) => console.error(...args),
        info: (...args) => console.info(...args)
    };

    /************************************************************
     * 顶部提示条（3秒自动消失）
     ************************************************************/
    const statusDiv = document.createElement("div");
    statusDiv.id = "ai-monitor-status";
    Object.assign(statusDiv.style, {
        position: "fixed",
        top: "10px",
        right: "10px",
        zIndex: "9999",
        backgroundColor: "green",
        color: "white",
        padding: "10px",
        borderRadius: "5px",
        fontSize: "14px",
        boxShadow: "0 0 10px rgba(0,0,0,0.5)",
        display: "none"
    });
    document.body.appendChild(statusDiv);

    let lastMessage = "";
    const showStatusMessage = (message, color = "green") => {
        if (lastMessage === message) return;
        lastMessage = message;
        statusDiv.style.backgroundColor = color;
        statusDiv.innerText = message;
        statusDiv.style.display = "block";

        setTimeout(() => {
            statusDiv.style.display = "none";
            lastMessage = "";
        }, 3000);
    };

    /************************************************************
     * 一些辅助函数
     ************************************************************/
    const getStatusText = (statusKey) => {
        const statusMap = {
            "message_group_name_waitReply": "待回复",
            "message_group_name_humanReply": "人工已回复",
            "message_group_name_autoReply": "机器人已回复",
            "message_group_name_overThreemins": "超时待回复"
        };
        return statusMap[statusKey] || "未知状态";
    };

    const debounce = (func, delay) => {
        let timeout;
        return (...args) => {
            clearTimeout(timeout);
            timeout = setTimeout(() => func(...args), delay);
        };
    };

    const getUsernameAndMessage = (userItem) => {
        const usernameElement = userItem.querySelector('[class*="ACbDgAKLLGYz9MPeTYzw"] span');
        const username = usernameElement ? usernameElement.textContent.trim() : "未知用户";
        const messageElement = userItem.querySelector('[class*="qn15NQQrTVBNHreW_xHW"]');
        let message = "无消息";
        if (messageElement) {
            const spans = Array.from(messageElement.querySelectorAll("span"));
            message = spans.map(span => span.textContent.trim()).filter(text => text).join(" ");
        }
        return { username, message };
    };

    /************************************************************
     * 控制台输出更新
     ************************************************************/
    const updateConsoleOutput = () => {
        console.clear();
        aiLogger.log("%c[AI监控] 抖店飞鸽AI自动聊天监控", "font-weight:bold;color:black;");

        if (isNoUser) {
            aiLogger.log("%c[AI监控] 暂无会话中用户", "color:red;font-weight:bold;");
        }

        for (const [statusKey, { statusText, users }] of Object.entries(lastStatusMap)) {
            aiLogger.log(`%c[AI监控] 切换到状态：${statusText}`, "color:red;font-weight:bold;");
            for (const [username, message] of Object.entries(users)) {
                aiLogger.log(`%c[AI监控] ${statusText}-【${username}】：${message}`, "color:green;font-weight:bold;");
                if (lastOrderInfo && lastOrderInfo.username === username) {
                    if (lastOrderInfo.noOrders) {
                        aiLogger.log(`%c[AI监控] 【${lastOrderInfo.username}】：暂无180天内订单`, "color:blue;font-weight:bold;");
                    } else if (lastOrderInfo.orders) {
                        lastOrderInfo.orders.forEach((order) => {
                            aiLogger.log(`%c[AI监控] ${order.status}-【${order.username}】：${order.name}（订单：${order.orderNumber}）`, "color:blue;font-weight:bold;");
                        });
                    }
                }
            }
        }

        const isUserInRealtime = Object.values(lastStatusMap).some(({ users }) => lastOrderInfo && lastOrderInfo.username in users);
        if (lastOrderInfo && !isUserInRealtime) {
            aiLogger.log("%c[AI监控] 选中用户订单信息：", "color:gray;font-weight:bold;");
            if (lastOrderInfo.noOrders) {
                aiLogger.log(`%c[AI监控] 【${lastOrderInfo.username}】：暂无180天内订单`, "color:gray;font-weight:bold;");
            } else if (lastOrderInfo.orders) {
                lastOrderInfo.orders.forEach((order) => {
                    aiLogger.log(`%c[AI监控] ${order.status}-【${order.username}】：${order.name}（订单：${order.orderNumber}）`, "color:gray;font-weight:bold;");
                });
            }
        }

        if (lastChatMessages && lastChatMessages.length > 0) {
            aiLogger.log(`%c[AI监控] 【${lastSelectedUser}】聊天记录：`, "color:blue;font-weight:bold;");
            lastChatMessages.forEach((msg) => {
                let messageColor = msg.sender === '用户' ? 'gray' : 'black';
                let prefix = `【${msg.sender} ${msg.time || ''}】：`;
                if (msg.type === '文本') {
                    aiLogger.log(`%c[AI监控] ${prefix}${msg.content}`, `color:${messageColor};font-weight:bold;`);
                } else if (msg.type === '图片') {
                    aiLogger.log(`%c[AI监控] ${prefix}[图片] - ${msg.content}`, `color:${messageColor};font-weight:bold;`);
                } else if (msg.type === '视频') {
                    aiLogger.log(`%c[AI监控] ${prefix}[视频] - ${msg.content}`, `color:${messageColor};font-weight:bold;`);
                } else {
                    aiLogger.log(`%c[AI监控] ${prefix}[未知类型]`, `color:${messageColor};font-weight:bold;`);
                }
            });
        }
    };

    /************************************************************
     * 处理聊天列表
     ************************************************************/
    const processChatList = (chatListContainer) => {
        try {
            // 检查是否有聊天项目，如果没有任何聊天项目则认为无用户
            const chatItems = chatListContainer.querySelectorAll("div[style*='position: absolute']");
            const hasUsers = chatItems.length > 0 && Array.from(chatItems).some(item =>
                item.querySelector('[data-btm-id]') || item.querySelector('span[data-btm^="message_group_name_"]')
            );

            if (!hasUsers) {
                // 无会话
                if (!isNoUser) {
                    isNoUser = true;
                    lastStatusMap = {};
                    showStatusMessage("暂无会话中用户", "red");
                    updateConsoleOutput();
                }
            } else {
                // 有会话
                if (isNoUser) {
                    isNoUser = false;
                    showStatusMessage("有用户会话", "green");
                }

                // 更新选择器：适配新的DOM结构
                const chatItems = chatListContainer.querySelectorAll("div[style*='position: absolute']");
                if (chatItems.length > 0) {
                    let currentStatusKey = null;
                    let statusMap = {};

                    chatItems.forEach(item => {
                        const statusSpan = item.querySelector('span[data-btm^="message_group_name_"]');
                        if (statusSpan) {
                            currentStatusKey = statusSpan.getAttribute("data-btm");
                            const statusText = getStatusText(currentStatusKey);
                            if (!statusMap[currentStatusKey]) {
                                statusMap[currentStatusKey] = { statusText, users: {} };
                            }
                        } else if (item.querySelector('[data-btm-id]')) {
                            const userItem = item.querySelector('[data-btm-id]');
                            if (userItem && currentStatusKey) {
                                const { username, message } = getUsernameAndMessage(userItem);
                                statusMap[currentStatusKey].users[username] = message;
                            }
                        }
                    });

                    if (JSON.stringify(statusMap) !== JSON.stringify(lastStatusMap)) {
                        lastStatusMap = statusMap;
                        showStatusMessage("聊天状态已更新", "green");
                        updateConsoleOutput();
                    } else {
                        updateConsoleOutput();
                    }
                }
            }

            // 获取当前选中用户
            const selectedUserElement = document.querySelector('div[data-qa-id="qa-user-portrait-username"]');
            if (selectedUserElement) {
                const username = selectedUserElement.textContent.trim();
                if (username && username !== lastSelectedUser) {
                    // 切换选中用户
                    lastSelectedUser = username;
                    lastOrderInfo = null;
                    lastChatMessages = [];
                    observeOrderInfo(username);
                    observeChatMessages();
                } else {
                    if (username && !lastOrderInfo) {
                        observeOrderInfo(username);
                    }
                    if (username && (!lastChatMessages || lastChatMessages.length === 0)) {
                        observeChatMessages();
                    }
                }
            } else {
                // 无选中用户
                waitForSelectedUser();
            }

            autoReplyToWaitReplyUsers();

        } catch (error) {
            aiLogger.error(`[AI监控] 处理聊天列表时出错：${error.message}`);
        }
    };

    const waitForSelectedUser = () => {
        const selectedUserCheckInterval = setInterval(() => {
            const selectedUserElement = document.querySelector('div[data-qa-id="qa-user-portrait-username"]');
            if (selectedUserElement) {
                clearInterval(selectedUserCheckInterval);
                const username = selectedUserElement.textContent.trim();
                lastSelectedUser = username;
                lastOrderInfo = null;
                lastChatMessages = [];
                observeOrderInfo(username);
                observeChatMessages();
            }
        }, 500);
    };

    /************************************************************
     * 监控订单信息
     ************************************************************/
    const observeOrderInfo = (username) => {
        const orderContainer = document.querySelector("#mona-workbench_订单");
        if (orderContainer) {
            if (observeOrderInfo.orderObserver) {
                observeOrderInfo.orderObserver.disconnect();
            }

            showStatusMessage("订单信息加载中...", "blue");
            const orderObserver = new MutationObserver(() => {
                if (lastSelectedUser) {
                    const orderLoaded = fetchOrderInfo(lastSelectedUser);
                    if (orderLoaded) {
                        orderObserver.disconnect();
                        showStatusMessage("订单信息已更新", "green");
                    }
                }
            });

            orderObserver.observe(orderContainer, {
                childList: true,
                subtree: true,
                attributes: true,
                characterData: true
            });

            observeOrderInfo.orderObserver = orderObserver;

            const initialOrderLoaded = fetchOrderInfo(username);
            if (initialOrderLoaded) {
                orderObserver.disconnect();
                showStatusMessage("订单信息已更新", "green");
            }
        } else {
            waitForOrderContainer(username);
        }
    };

    const waitForOrderContainer = (username) => {
        const orderContainerCheckInterval = setInterval(() => {
            const orderContainer = document.querySelector("#mona-workbench_订单");
            if (orderContainer) {
                clearInterval(orderContainerCheckInterval);
                observeOrderInfo(username);
            }
        }, 500);
    };

    const fetchOrderInfo = (username) => {
        try {
            const orderContainer = document.querySelector('#mona-workbench_订单');
            if (!orderContainer) return false;

            const noOrderElement = orderContainer.querySelector('div.ecom-empty-description');
            if (noOrderElement && noOrderElement.textContent.trim() === '暂无180天内订单') {
                const orderInfo = { username, orders: [], noOrders: true };
                if (JSON.stringify(orderInfo) !== JSON.stringify(lastOrderInfo)) {
                    lastOrderInfo = orderInfo;
                    updateConsoleOutput();
                    showStatusMessage("暂无180天内订单", "orange");
                }
                return true;
            }

            const orders = orderContainer.querySelectorAll('div.ecom-collapse-item');
            if (orders.length === 0) return false;

            const orderInfoList = [];
            orders.forEach((order, index) => {
                const orderStatusElement = order.querySelector('div.ecom-sp-tag .sp-tag-content');
                const orderStatus = orderStatusElement ? orderStatusElement.textContent.trim() : '未知状态';

                const orderNameElement = order.querySelector('.textSpecial > div > span');
                const orderName = orderNameElement ? orderNameElement.textContent.trim() : '未知商品';

                if (orderStatus && orderName) {
                    orderInfoList.push({
                        status: orderStatus,
                        name: orderName,
                        username: username,
                        orderNumber: index + 1
                    });
                }
            });

            if (orderInfoList.length > 0) {
                const orderInfo = { username, orders: orderInfoList, noOrders: false };
                if (JSON.stringify(orderInfo) !== JSON.stringify(lastOrderInfo)) {
                    lastOrderInfo = orderInfo;
                    updateConsoleOutput();
                    showStatusMessage("订单信息已更新", "orange");
                }
                return true;
            } else {
                return false;
            }
        } catch (error) {
            aiLogger.error(`[AI监控] 获取订单信息时出错：${error.message}`);
            return false;
        }
    };

    /************************************************************
     * 监控聊天消息
     ************************************************************/
    const observeChatMessages = () => {
        const messageListContainer = document.querySelector('.messageList');
        if (!messageListContainer) {
            waitForMessageListContainer();
            return;
        }

        if (observeChatMessages.chatObserver) {
            observeChatMessages.chatObserver.disconnect();
        }

        const chatObserver = new MutationObserver(() => {
            const messages = fetchChatMessages();
            if (messages) {
                lastChatMessages = messages;
                updateConsoleOutput();
            }
        });

        chatObserver.observe(messageListContainer, {
            childList: true,
            subtree: true,
            characterData: true,
            attributes: true
        });

        observeChatMessages.chatObserver = chatObserver;

        const initialMessages = fetchChatMessages();
        if (initialMessages) {
            lastChatMessages = initialMessages;
            updateConsoleOutput();
        }
    };

    const waitForMessageListContainer = () => {
        const messageListCheckInterval = setInterval(() => {
            const messageListContainer = document.querySelector('.messageList');
            if (messageListContainer) {
                clearInterval(messageListCheckInterval);
                observeChatMessages();
            }
        }, 500);
    };

    const fetchChatMessages = () => {
        try {
            const messageListContainer = document.querySelector('.messageList');
            if (!messageListContainer) return [];
            const messageElements = messageListContainer.querySelectorAll('div[data-qa-id="qa-message-warpper"]');
            if (messageElements.length === 0) return [];

            const messages = [];
            messageElements.forEach(messageElement => {
                let messageType = '未知';
                let messageContent = '';
                let sender = '未知';

                const messageBubble = messageElement.querySelector('.Ie29C7uLyEjZzd8JeS8A');
                if (messageBubble) {
                    if (messageBubble.style.flexDirection === 'row') {
                        sender = '用户';
                    } else if (messageBubble.style.flexDirection === 'row-reverse') {
                        sender = '客服';
                    }
                }

                const messageContentElement = messageElement.querySelector('pre > span');
                if (messageContentElement) {
                    messageType = '文本';
                    messageContent = messageContentElement.textContent.trim();
                } else {
                    const imageElement = messageElement.querySelector('img.WOUA1PDG10lEGvmqa_W3, img.wLLihoJSQT_lF8636mkj');
                    if (imageElement) {
                        const overlayElement = messageElement.querySelector('img.rnXlFPvVe29ACsLydCWg');
                        if (overlayElement) {
                            messageType = '视频';
                            messageContent = imageElement.src;
                        } else {
                            messageType = '图片';
                            messageContent = imageElement.src;
                        }
                    }
                }

                let messageTime = '';
                const messageTimeElement = messageElement.querySelector('.O4UWWFoQxgMq4AWHMq25');
                if (messageTimeElement) {
                    messageTime = messageTimeElement.textContent.trim();
                }

                if (messageType !== '未知') {
                    messages.push({ sender, type: messageType, content: messageContent, time: messageTime });
                }
            });

            return messages;
        } catch (error) {
            aiLogger.error(`[AI监控] 获取聊天消息时出错：${error.message}`);
            return [];
        }
    };

    /************************************************************
     * 开始监听
     ************************************************************/
    const debounceProcessChatList = debounce(processChatList, 100);

    const startMonitoring = (chatListContainer) => {
        aiLogger.log("%c[AI监控] 聊天列表容器已加载，开始实时监测...", "color:black;");
        showStatusMessage("聊天列表容器已加载，开始监测！", "green");

        const observer = new MutationObserver(() => {
            debounceProcessChatList(chatListContainer);
        });

        observer.observe(chatListContainer, {
            childList: true,
            subtree: true,
            attributes: true,
            characterData: true
        });

        processChatList(chatListContainer);
    };

    const pollForChatList = () => {
        aiLogger.log("%c[AI监控] 启动容器轮询...", "color:black;");
        const pollInterval = setInterval(() => {
            const chatArea = document.querySelector("#chantListScrollArea");
            if (!chatArea) {
                return;
            }

            // 更新选择器：先尝试新的结构，再尝试旧的结构
            let chatListContainer = chatArea.querySelector(".list_items");
            if (!chatListContainer) {
                chatListContainer = chatArea.querySelector("[class*='ReactVirtualized__Grid__innerScrollContainer']");
            }

            if (!chatListContainer) {
                if (!isNoUser) {
                    isNoUser = true;
                    lastStatusMap = {};
                    showStatusMessage("暂无会话中用户", "red");
                    updateConsoleOutput();
                }
                // 继续观察聊天区域，等待用户出现
                observeChatArea(chatArea);
                return;
            }

            aiLogger.log("%c[AI监控] 聊天列表容器已找到！", "color:black;");
            showStatusMessage("聊天列表容器已找到！", "blue");
            clearInterval(pollInterval);

            startMonitoring(chatListContainer);
            observeChatArea(chatArea);
        }, 1000);
    };

    // 全局聊天区域观察器，防止重复创建
    let globalChatAreaObserver = null;

    const observeChatArea = (chatArea) => {
        // 如果已经有观察器在运行，先断开
        if (globalChatAreaObserver) {
            globalChatAreaObserver.disconnect();
        }

        globalChatAreaObserver = new MutationObserver(() => {
            // 更新选择器：先尝试新的结构，再尝试旧的结构
            let chatListContainer = chatArea.querySelector(".list_items");
            if (!chatListContainer) {
                chatListContainer = chatArea.querySelector("[class*='ReactVirtualized__Grid__innerScrollContainer']");
            }

            if (chatListContainer && isNoUser) {
                aiLogger.log("%c[AI监控] 聊天列表容器重新加载！从无用户状态恢复", "color:green;");
                showStatusMessage("检测到用户会话，开始监控！", "green");
                isNoUser = false;
                startMonitoring(chatListContainer);
            } else if (!chatListContainer && !isNoUser) {
                aiLogger.log("%c[AI监控] 聊天列表容器消失，进入无用户状态", "color:orange;");
                isNoUser = true;
                lastStatusMap = {};
                showStatusMessage("暂无会话中用户", "red");
                updateConsoleOutput();
            }
        });

        globalChatAreaObserver.observe(chatArea, {
            childList: true,
            subtree: true,
            attributes: true // 增加属性监听，确保能检测到更多变化
        });

        aiLogger.log("%c[AI监控] 聊天区域观察器已启动", "color:blue;");
    };

    // 监控恢复机制：定期检查监控状态
    const startMonitoringRecovery = () => {
        setInterval(() => {
            const chatArea = document.querySelector("#chantListScrollArea");
            if (!chatArea) {
                return;
            }

            // 检查是否有聊天列表容器
            let chatListContainer = chatArea.querySelector(".list_items");
            if (!chatListContainer) {
                chatListContainer = chatArea.querySelector("[class*='ReactVirtualized__Grid__innerScrollContainer']");
            }

            // 如果有容器但是处于无用户状态，说明监控可能失效了
            if (chatListContainer && isNoUser) {
                aiLogger.log("%c[AI监控] 检测到监控恢复条件，重新启动监控", "color:purple;");
                showStatusMessage("监控恢复：检测到用户会话", "green");
                isNoUser = false;
                startMonitoring(chatListContainer);
            }

            // 确保聊天区域观察器始终在运行
            if (!globalChatAreaObserver) {
                aiLogger.log("%c[AI监控] 聊天区域观察器丢失，重新启动", "color:purple;");
                observeChatArea(chatArea);
            }
        }, 5000); // 每5秒检查一次
    };

    // 检测手动回复完成（监听发送按钮点击或消息发送）
    const detectManualReplyCompletion = () => {
        // 监听发送按钮点击
        document.addEventListener('click', (event) => {
            const target = event.target;

            // 检查是否是发送按钮
            if (target.matches('button[data-qa-id="qa-send-btn"]') ||
                target.closest('button[data-qa-id="qa-send-btn"]')) {

                if (isManualReplying) {
                    aiLogger.info("[混合策略] 检测到手动回复发送，准备处理队列");

                    // 延迟一下确保消息发送完成
                    setTimeout(() => {
                        // 移除当前非白名单用户
                        if (nonWhitelistUserQueue.length > 0) {
                            const currentUser = nonWhitelistUserQueue[0];
                            removeUserFromQueue(currentUser);
                            aiLogger.info(`[混合策略] 非白名单用户 ${currentUser} 手动回复完成`);
                        }

                        // 重置手动处理状态
                        isManualReplying = false;
                        manualReplyStartTime = null;

                        // 继续处理队列
                        setTimeout(() => processQueuedUsers(), 500);
                    }, 1000);
                }
            }
        });

        // 监听键盘发送（Enter键）
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Enter' && !event.shiftKey && isManualReplying) {
                const target = event.target;

                // 检查是否在消息输入框中
                if (target.matches('textarea[data-qa-id="qa-send-textarea"]') ||
                    target.closest('textarea[data-qa-id="qa-send-textarea"]')) {

                    aiLogger.info("[混合策略] 检测到手动回复发送（Enter键），准备处理队列");

                    setTimeout(() => {
                        if (nonWhitelistUserQueue.length > 0) {
                            const currentUser = nonWhitelistUserQueue[0];
                            removeUserFromQueue(currentUser);
                            aiLogger.info(`[混合策略] 非白名单用户 ${currentUser} 手动回复完成`);
                        }

                        isManualReplying = false;
                        manualReplyStartTime = null;
                        setTimeout(() => processQueuedUsers(), 500);
                    }, 1000);
                }
            }
        });
    };

    const monitorOrderInfo = () => {
        if (isOrderMonitoring) return;
        isOrderMonitoring = true;

        const monitor = () => {
            const selectedUserElement = document.querySelector('div[data-qa-id="qa-user-portrait-username"]');
            if (selectedUserElement) {
                const username = selectedUserElement.textContent.trim();
                if (username && username !== lastSelectedUser) {
                    lastSelectedUser = username;
                    lastOrderInfo = null;
                    lastChatMessages = [];
                    observeOrderInfo(username);
                    observeChatMessages();
                } else {
                    if (username && !lastOrderInfo) {
                        observeOrderInfo(username);
                    }
                    if (username && (!lastChatMessages || lastChatMessages.length === 0)) {
                        observeChatMessages();
                    }
                }
            } else {
                waitForSelectedUser();
            }
        };

        const interval = setInterval(monitor, 1000);
        window.addEventListener('unload', () => clearInterval(interval));
    };

    /************************************************************
     * 以下是配置面板相关 DOM、UI 构建与逻辑
     * （分类组管理、关键词管理、导出导入、AI配置等）
     ************************************************************/
    let currentCategoryIndex = 0;
    let keywordListCollapsed = false; // 折叠状态

    // 这里包含了分类组UI、关键词列表UI、默认回复、AI接口配置、特殊按钮触发词展示等等
    // ------------------------- 配置面板容器 -------------------------
    const configContainer = document.createElement('div');
    configContainer.id = 'figeonConfigContainer';
    Object.assign(configContainer.style, {
        position: 'fixed',
        bottom: '50px',
        left: '50%',
        transform: 'translateX(-50%)',
        zIndex: 9999,
        background: '#fff',
        borderRadius: '10px',
        boxShadow: '0 0 10px rgba(0,0,0,0.3)',
        padding: '20px',
        minWidth: '600px',
        maxWidth: '800px',
        fontFamily: 'Arial, sans-serif',
        display: 'none',
        color: '#333'
    });

    /************************************************************
     * 1. 标题 + 简介 + 自动关闭会话按钮
     ************************************************************/
    const titleContainer = document.createElement('div');
    Object.assign(titleContainer.style, {
        display: 'flex',
        alignItems: 'center',
        marginBottom: '10px',
        position: 'relative'
    });

    const title = document.createElement('h3');
    title.textContent = 'AI自动回复配置';
    title.style.marginTop = '0';
    title.style.marginBottom = '0';
    title.style.marginRight = '20px'; // 给右侧留出空间

    // 自动关闭会话开关容器 - 放在标题右侧
    const autoCloseContainer = document.createElement('div');
    Object.assign(autoCloseContainer.style, {
        display: 'flex',
        alignItems: 'center',
        gap: '8px',
        marginLeft: 'auto', // 推到右侧
        marginRight: '160px' // 避免与右上角的"查看特殊按钮触发词"按钮重叠
    });

    const autoCloseCheckbox = document.createElement('input');
    autoCloseCheckbox.type = 'checkbox';
    autoCloseCheckbox.id = 'autoCloseSessionCheckbox';
    autoCloseCheckbox.checked = aiConfig.autoCloseSession || false;
    autoCloseCheckbox.addEventListener('change', () => {
        aiConfig.autoCloseSession = autoCloseCheckbox.checked;
        saveConfigToStorage();
        showStatusMessage(
            aiConfig.autoCloseSession ? "已启用自动关闭会话" : "已关闭自动关闭会话",
            aiConfig.autoCloseSession ? "green" : "orange"
        );
    });

    const autoCloseLabel = document.createElement('label');
    autoCloseLabel.htmlFor = 'autoCloseSessionCheckbox';
    autoCloseLabel.textContent = '自动关闭会话';
    Object.assign(autoCloseLabel.style, {
        cursor: 'pointer',
        fontSize: '14px',
        fontWeight: 'bold',
        color: '#2196F3',
        whiteSpace: 'nowrap' // 防止文字换行
    });

    autoCloseContainer.appendChild(autoCloseCheckbox);
    autoCloseContainer.appendChild(autoCloseLabel);

    titleContainer.appendChild(title);
    titleContainer.appendChild(autoCloseContainer);
    configContainer.appendChild(titleContainer);

    const desc = document.createElement('p');
    desc.textContent = '通过分类组及关键词配置实现智能回复。右键分类组可编辑，点击分类组右上角X可删除，拖动分类组按钮进行排序。';
    desc.style.fontSize = '14px';
    desc.style.color = '#666';
    configContainer.appendChild(desc);

    /************************************************************
     * 2. 分类组导航区
     ************************************************************/
    const categoryNavContainer = document.createElement('div');
    Object.assign(categoryNavContainer.style, {
        display: 'flex',
        flexWrap: 'wrap',
        gap: '5px',
        marginBottom: '10px',
        maxWidth: '100%'
    });
    configContainer.appendChild(categoryNavContainer);

    let dragStartIndex = null;

    function renderCategoryNav() {
        categoryNavContainer.innerHTML = '';
        aiConfig.categories.sort((a, b) => a.order - b.order);

        aiConfig.categories.forEach((cat, index) => {
            const catWrapper = document.createElement('div');
            catWrapper.style.position = 'relative';
            catWrapper.style.display = 'inline-flex';
            catWrapper.style.alignItems = 'center';
            catWrapper.style.border = '1px solid #ddd';
            catWrapper.style.borderRadius = '5px';
            catWrapper.style.padding = '0';
            catWrapper.style.backgroundColor = index === currentCategoryIndex ? '#1966ff' : '#f0f0f0';
            catWrapper.draggable = true;
            catWrapper.dataset.index = index;

            catWrapper.addEventListener('dragstart', (e) => {
                dragStartIndex = parseInt(catWrapper.dataset.index, 10);
                e.dataTransfer.effectAllowed = 'move';
                e.dataTransfer.setData('text/plain', '');
                catWrapper.style.opacity = '0.5';
            });

            catWrapper.addEventListener('dragend', () => {
                catWrapper.style.opacity = '1';
            });

            catWrapper.addEventListener('dragover', (e) => {
                e.preventDefault();
            });

            catWrapper.addEventListener('drop', (e) => {
                e.preventDefault();
                const dropIndex = parseInt(catWrapper.dataset.index, 10);
                if (dragStartIndex !== null && dragStartIndex !== dropIndex) {
                    const movedCat = aiConfig.categories.splice(dragStartIndex, 1)[0];
                    aiConfig.categories.splice(dropIndex, 0, movedCat);
                    aiConfig.categories.forEach((c, i) => c.order = i);
                    dragStartIndex = null;
                    renderCategoryNav();
                    renderKeywords();
                }
            });

            const catBtn = document.createElement('button');
            catBtn.textContent = cat.name;
            Object.assign(catBtn.style, {
                backgroundColor: 'transparent',
                color: index === currentCategoryIndex ? '#fff' : '#333',
                border: 'none',
                borderRadius: '0',
                padding: '5px 10px',
                cursor: 'pointer',
                userSelect: 'none',
                maxWidth: '100px',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
            });

            catBtn.addEventListener('click', () => {
                currentCategoryIndex = index;
                renderCategoryNav();
                renderKeywords();
            });

            catBtn.addEventListener('contextmenu', (e) => {
                e.preventDefault();
                showEditCategoryOverlay(index);
            });

            catWrapper.appendChild(catBtn);

            if (!cat.isDefault) {
                const delBtn = document.createElement('div');
                delBtn.textContent = '×';
                Object.assign(delBtn.style, {
                    position: 'absolute',
                    top: '2px',
                    right: '2px',
                    width: '15px',
                    height: '15px',
                    background: 'red',
                    color: '#fff',
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    cursor: 'pointer',
                    fontWeight: 'bold',
                    fontSize: '12px'
                });
                delBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    if (confirm(`确认删除分类组"${cat.name}"吗？`)) {
                        aiConfig.categories.splice(index, 1);
                        if (currentCategoryIndex >= aiConfig.categories.length) {
                            currentCategoryIndex = aiConfig.categories.length - 1;
                        }
                        renderCategoryNav();
                        renderKeywords();
                    }
                });
                catWrapper.appendChild(delBtn);
            }

            categoryNavContainer.appendChild(catWrapper);
        });

        // 新增分类组按钮
        const addCategoryBtn = document.createElement('button');
        addCategoryBtn.textContent = '+新增分类组';
        Object.assign(addCategoryBtn.style, {
            backgroundColor: '#28a745',
            color: '#fff',
            border: 'none',
            borderRadius: '5px',
            padding: '5px 10px',
            cursor: 'pointer'
        });
        addCategoryBtn.addEventListener('click', () => {
            showAddCategoryOverlay();
        });
        categoryNavContainer.appendChild(addCategoryBtn);
    }

    // 编辑分类组弹窗
    let editCategoryOverlay = null;
    function showEditCategoryOverlay(index) {
        const category = aiConfig.categories[index];
        if (!editCategoryOverlay) {
            editCategoryOverlay = document.createElement('div');
            Object.assign(editCategoryOverlay.style, {
                position: 'fixed',
                top: '0',
                left: '0',
                width: '100%',
                height: '100%',
                backgroundColor: 'rgba(0,0,0,0.5)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                zIndex: 10000
            });

            const overlayContent = document.createElement('div');
            Object.assign(overlayContent.style, {
                background: '#fff',
                padding: '20px',
                borderRadius: '10px',
                boxShadow: '0 0 10px rgba(0,0,0,0.3)',
                minWidth: '300px'
            });

            const overlayTitle = document.createElement('h3');
            overlayTitle.textContent = '编辑分类组';
            overlayContent.appendChild(overlayTitle);

            const nameInput = document.createElement('input');
            nameInput.type = 'text';
            nameInput.placeholder = '分类组名称';
            Object.assign(nameInput.style, {
                width: '100%',
                marginBottom: '10px',
                boxSizing: 'border-box',
                padding: '5px'
            });
            overlayContent.appendChild(nameInput);

            const keywordInput = document.createElement('input');
            keywordInput.type = 'text';
            keywordInput.placeholder = '分类组关键词';
            Object.assign(keywordInput.style, {
                width: '100%',
                marginBottom: '10px',
                boxSizing: 'border-box',
                padding: '5px'
            });
            overlayContent.appendChild(keywordInput);

            const orderInput = document.createElement('input');
            orderInput.type = 'number';
            orderInput.placeholder = '排序值(数字越小越前)';
            Object.assign(orderInput.style, {
                width: '100%',
                marginBottom: '10px',
                boxSizing: 'border-box',
                padding: '5px'
            });
            overlayContent.appendChild(orderInput);

            const btnContainer = document.createElement('div');
            btnContainer.style.textAlign = 'right';

            const confirmBtn = document.createElement('button');
            confirmBtn.textContent = '确认';
            Object.assign(confirmBtn.style, {
                backgroundColor: '#1966ff',
                color: '#fff',
                border: 'none',
                borderRadius: '5px',
                padding: '5px 10px',
                marginRight: '10px',
                cursor: 'pointer'
            });
            confirmBtn.addEventListener('click', () => {
                const nameVal = nameInput.value.trim();
                const keywordVal = keywordInput.value.trim();
                const orderVal = parseInt(orderInput.value.trim() || "999", 10);
                if (!nameVal) {
                    alert('请输入分类组名称');
                    return;
                }
                category.name = nameVal;
                category.keyword = keywordVal;
                category.order = orderVal;
                aiConfig.categories.sort((a, b) => a.order - b.order);
                renderCategoryNav();
                renderKeywords();
                editCategoryOverlay.style.display = 'none';
            });

            const cancelBtn = document.createElement('button');
            cancelBtn.textContent = '取消';
            Object.assign(cancelBtn.style, {
                backgroundColor: '#aaa',
                color: '#fff',
                border: 'none',
                borderRadius: '5px',
                padding: '5px 10px',
                cursor: 'pointer'
            });
            cancelBtn.addEventListener('click', () => {
                editCategoryOverlay.style.display = 'none';
            });

            btnContainer.appendChild(confirmBtn);
            btnContainer.appendChild(cancelBtn);
            overlayContent.appendChild(btnContainer);

            editCategoryOverlay.appendChild(overlayContent);
            document.body.appendChild(editCategoryOverlay);

            editCategoryOverlay._nameInput = nameInput;
            editCategoryOverlay._keywordInput = keywordInput;
            editCategoryOverlay._orderInput = orderInput;
        }

        editCategoryOverlay._nameInput.value = category.name;
        editCategoryOverlay._keywordInput.value = category.keyword;
        editCategoryOverlay._orderInput.value = category.order;
        editCategoryOverlay.style.display = 'flex';
    }

    // 新增分类组弹窗
    let addCategoryOverlay = null;
    function showAddCategoryOverlay() {
        if (!addCategoryOverlay) {
            addCategoryOverlay = document.createElement('div');
            Object.assign(addCategoryOverlay.style, {
                position: 'fixed',
                top: '0',
                left: '0',
                width: '100%',
                height: '100%',
                backgroundColor: 'rgba(0,0,0,0.5)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                zIndex: 10000
            });

            const overlayContent = document.createElement('div');
            Object.assign(overlayContent.style, {
                background: '#fff',
                padding: '20px',
                borderRadius: '10px',
                boxShadow: '0 0 10px rgba(0,0,0,0.3)',
                minWidth: '300px'
            });

            const overlayTitle = document.createElement('h3');
            overlayTitle.textContent = '新增分类组';
            overlayContent.appendChild(overlayTitle);

            const nameInput = document.createElement('input');
            nameInput.type = 'text';
            nameInput.placeholder = '分类组名称';
            Object.assign(nameInput.style, {
                width: '100%',
                marginBottom: '10px',
                boxSizing: 'border-box',
                padding: '5px'
            });
            overlayContent.appendChild(nameInput);

            const keywordInput = document.createElement('input');
            keywordInput.type = 'text';
            keywordInput.placeholder = '分类组关键词';
            Object.assign(keywordInput.style, {
                width: '100%',
                marginBottom: '10px',
                boxSizing: 'border-box',
                padding: '5px'
            });
            overlayContent.appendChild(keywordInput);

            const maxOrder = aiConfig.categories.length > 0 ? Math.max(...aiConfig.categories.map(c => c.order)) : 0;

            const orderInput = document.createElement('input');
            orderInput.type = 'number';
            orderInput.placeholder = '排序值(数字越小越前)';
            orderInput.value = maxOrder + 1;
            Object.assign(orderInput.style, {
                width: '100%',
                marginBottom: '10px',
                boxSizing: 'border-box',
                padding: '5px'
            });
            overlayContent.appendChild(orderInput);

            const btnContainer = document.createElement('div');
            btnContainer.style.textAlign = 'right';

            const confirmBtn = document.createElement('button');
            confirmBtn.textContent = '确认';
            Object.assign(confirmBtn.style, {
                backgroundColor: '#1966ff',
                color: '#fff',
                border: 'none',
                borderRadius: '5px',
                padding: '5px 10px',
                marginRight: '10px',
                cursor: 'pointer'
            });
            confirmBtn.addEventListener('click', () => {
                const nameVal = nameInput.value.trim();
                const keywordVal = keywordInput.value.trim();
                const orderVal = parseInt(orderInput.value.trim() || "999", 10);
                if (!nameVal) {
                    alert('请输入分类组名称');
                    return;
                }
                aiConfig.categories.push({
                    name: nameVal,
                    keyword: keywordVal,
                    order: orderVal,
                    keywords: []
                });
                aiConfig.categories.sort((a, b) => a.order - b.order);
                renderCategoryNav();
                renderKeywords();
                addCategoryOverlay.style.display = 'none';
            });

            const cancelBtn = document.createElement('button');
            cancelBtn.textContent = '取消';
            Object.assign(cancelBtn.style, {
                backgroundColor: '#aaa',
                color: '#fff',
                border: 'none',
                borderRadius: '5px',
                padding: '5px 10px',
                cursor: 'pointer'
            });
            cancelBtn.addEventListener('click', () => {
                addCategoryOverlay.style.display = 'none';
            });

            btnContainer.appendChild(confirmBtn);
            btnContainer.appendChild(cancelBtn);
            overlayContent.appendChild(btnContainer);

            addCategoryOverlay.appendChild(overlayContent);
            document.body.appendChild(addCategoryOverlay);

            addCategoryOverlay._nameInput = nameInput;
            addCategoryOverlay._keywordInput = keywordInput;
            addCategoryOverlay._orderInput = orderInput;
        }

        const maxOrder = aiConfig.categories.length > 0 ? Math.max(...aiConfig.categories.map(c => c.order)) : 0;
        addCategoryOverlay._nameInput.value = '';
        addCategoryOverlay._keywordInput.value = '';
        addCategoryOverlay._orderInput.value = maxOrder + 1;
        addCategoryOverlay.style.display = 'flex';
    }

    /************************************************************
     * 3. 关键词回复列表
     ************************************************************/
    const keywordListContainer = document.createElement('div');
    Object.assign(keywordListContainer.style, {
        border: '1px solid #ccc',
        borderRadius: '5px',
        padding: '10px',
        marginBottom: '10px',
        maxHeight: '300px',
        overflowY: 'auto'
    });

    const keywordListHeader = document.createElement('div');
    keywordListHeader.style.display = 'flex';
    keywordListHeader.style.justifyContent = 'space-between';
    keywordListHeader.style.alignItems = 'center';

    const keywordListTitle = document.createElement('h4');
    keywordListTitle.textContent = '关键词回复列表';
    keywordListTitle.style.margin = '0';
    keywordListHeader.appendChild(keywordListTitle);

    const collapseBtn = document.createElement('button');
    collapseBtn.textContent = keywordListCollapsed ? '▼' : '▲';
    Object.assign(collapseBtn.style, {
        backgroundColor: '#ccc',
        border: 'none',
        borderRadius: '5px',
        padding: '5px 10px',
        cursor: 'pointer',
        fontSize: '12px'
    });
    collapseBtn.addEventListener('click', () => {
        keywordListCollapsed = !keywordListCollapsed;
        collapseBtn.textContent = keywordListCollapsed ? '▼' : '▲';
        renderKeywords();
    });
    keywordListHeader.appendChild(collapseBtn);

    keywordListContainer.appendChild(keywordListHeader);

    const addKeywordBtn = document.createElement('button');
    addKeywordBtn.textContent = '添加关键词';
    Object.assign(addKeywordBtn.style, {
        marginBottom: '10px',
        display: 'block',
        backgroundColor: '#1966ff',
        color: '#fff',
        border: 'none',
        borderRadius: '5px',
        padding: '5px 10px',
        cursor: 'pointer'
    });

    const keywordItemsContainer = document.createElement('div');

    function renderKeywords() {
        keywordItemsContainer.innerHTML = '';
        const currentCategory = aiConfig.categories[currentCategoryIndex];

        if (!keywordListCollapsed) {
            currentCategory.keywords.forEach((item, index) => {
                const itemDiv = document.createElement('div');
                itemDiv.style.marginBottom = '10px';
                itemDiv.style.border = '1px solid #eee';
                itemDiv.style.padding = '10px';
                itemDiv.style.borderRadius = '5px';
                itemDiv.style.position = 'relative';

                const removeBtn = document.createElement('button');
                removeBtn.textContent = '删除';
                Object.assign(removeBtn.style, {
                    position: 'absolute',
                    top: '10px',
                    right: '10px',
                    background: 'red',
                    color: '#fff',
                    border: 'none',
                    borderRadius: '5px',
                    cursor: 'pointer'
                });
                removeBtn.addEventListener('click', () => {
                    currentCategory.keywords.splice(index, 1);
                    renderKeywords();
                });
                itemDiv.appendChild(removeBtn);

                const keywordInput = document.createElement('input');
                keywordInput.type = 'text';
                keywordInput.value = item.keyword;
                keywordInput.placeholder = '关键词';
                Object.assign(keywordInput.style, {
                    width: '100%',
                    padding: '5px',
                    marginBottom: '5px',
                    boxSizing: 'border-box'
                });
                keywordInput.addEventListener('input', () => {
                    currentCategory.keywords[index].keyword = keywordInput.value.trim();
                });
                itemDiv.appendChild(keywordInput);

                // 移除replyType选择器，统一使用"文本+文件"模式
                // 为兼容旧配置，保留item.replyType字段但不再显示选择器

                const replyInput = document.createElement('input');
                replyInput.type = 'text';
                replyInput.value = item.text || '';
                replyInput.placeholder = '回复内容（文本，可为空）';
                Object.assign(replyInput.style, {
                    width: '100%',
                    padding: '5px',
                    marginBottom: '5px',
                    boxSizing: 'border-box'
                });
                replyInput.addEventListener('input', () => {
                    currentCategory.keywords[index].text = replyInput.value.trim();
                });
                itemDiv.appendChild(replyInput);

                const mediaBtnContainer = document.createElement('div');
                mediaBtnContainer.style.marginBottom = '5px';

                const imageUploadBtn = document.createElement('button');
                imageUploadBtn.textContent = '上传图片';
                Object.assign(imageUploadBtn.style, {
                    backgroundColor: '#28a745',
                    color: '#fff',
                    border: 'none',
                    borderRadius: '5px',
                    padding: '5px 10px',
                    cursor: 'pointer',
                    marginRight: '5px'
                });
                imageUploadBtn.addEventListener('click', () => {
                    const fileInput = document.createElement('input');
                    fileInput.type = 'file';
                    fileInput.accept = 'image/*';
                    fileInput.multiple = true;
                    fileInput.onchange = () => {
                        const files = Array.from(fileInput.files);
                        const promises = [];

                        files.forEach(file => {
                            const reader = new FileReader();
                            const promise = new Promise((resolve) => {
                                reader.onload = async (e) => {
                                const base64 = e.target.result;
                                    try {
                                        // 保存到IndexedDB中，并获取媒体ID
                                        const mediaId = await saveMediaToDB(base64, 'image');
                                if (!currentCategory.keywords[index].images) {
                                    currentCategory.keywords[index].images = [];
                                }
                                        currentCategory.keywords[index].images.push(mediaId);
                                        resolve();
                                    } catch (error) {
                                        console.error('[AI监控] 保存图片失败:', error);
                                        resolve();
                                    }
                                };
                            });
                            promises.push(promise);
                            reader.readAsDataURL(file);
                        });

                        // 所有图片处理完成后重新渲染
                        Promise.all(promises).then(() => {
                            renderKeywords();
                        });
                    };
                    fileInput.click();
                });
                mediaBtnContainer.appendChild(imageUploadBtn);

                const videoUploadBtn = document.createElement('button');
                videoUploadBtn.textContent = '上传视频';
                Object.assign(videoUploadBtn.style, {
                    backgroundColor: '#17a2b8',
                    color: '#fff',
                    border: 'none',
                    borderRadius: '5px',
                    padding: '5px 10px',
                    cursor: 'pointer'
                });
                videoUploadBtn.addEventListener('click', () => {
                    const fileInput = document.createElement('input');
                    fileInput.type = 'file';
                    fileInput.accept = 'video/*';
                    fileInput.multiple = true;
                    fileInput.onchange = () => {
                        const files = Array.from(fileInput.files);
                        const promises = [];

                        files.forEach(file => {
                            const reader = new FileReader();
                            const promise = new Promise((resolve) => {
                                reader.onload = async (e) => {
                                const base64 = e.target.result;
                                    try {
                                        // 保存到IndexedDB中，并获取媒体ID
                                        const mediaId = await saveMediaToDB(base64, 'video');
                                if (!currentCategory.keywords[index].videos) {
                                    currentCategory.keywords[index].videos = [];
                                }
                                        currentCategory.keywords[index].videos.push(mediaId);
                                        resolve();
                                    } catch (error) {
                                        console.error('[AI监控] 保存视频失败:', error);
                                        resolve();
                                    }
                                };
                            });
                            promises.push(promise);
                            reader.readAsDataURL(file);
                        });

                        // 所有视频处理完成后重新渲染
                        Promise.all(promises).then(() => {
                            renderKeywords();
                        });
                    };
                    fileInput.click();
                });
                mediaBtnContainer.appendChild(videoUploadBtn);

                itemDiv.appendChild(mediaBtnContainer);

                // 显示图片、视频缩略图并支持删除
                if (item.images && item.images.length > 0) {
                    const imagesPreview = document.createElement('div');
                    imagesPreview.style.display = 'flex';
                    imagesPreview.style.flexWrap = 'wrap';
                    imagesPreview.style.marginTop = '5px';

                    // 创建一个加载状态显示
                    const loadingDiv = document.createElement('div');
                    loadingDiv.textContent = '正在加载媒体文件...';
                    loadingDiv.style.padding = '5px';
                    loadingDiv.style.color = '#666';
                    imagesPreview.appendChild(loadingDiv);
                    itemDiv.appendChild(imagesPreview);

                    // 异步加载所有图片
                    (async () => {
                        try {
                            // 批量加载所有图片数据
                            const imageDataList = await loadMediaBatch(item.images);

                            // 清除加载状态
                            imagesPreview.innerHTML = '';

                            // 显示所有图片
                            imageDataList.forEach((imgData, imgIndex) => {
                                if (!imgData) {
                                    console.warn(`[AI监控] 图片 ${item.images[imgIndex]} 加载失败`);
                                    return;
                                }

                        const imgWrapper = document.createElement('div');
                        imgWrapper.style.position = 'relative';
                        imgWrapper.style.marginRight = '5px';
                        imgWrapper.style.marginBottom = '5px';

                        const img = document.createElement('img');
                                img.src = imgData; // 使用加载的数据
                        img.style.width = '50px';
                        img.style.height = '50px';
                        img.style.objectFit = 'cover';
                        img.style.borderRadius = '5px';
                        imgWrapper.appendChild(img);

                        const delImgBtn = document.createElement('div');
                        delImgBtn.textContent = '×';
                        Object.assign(delImgBtn.style, {
                            position: 'absolute',
                            top: '0',
                            right: '0',
                            width: '15px',
                            height: '15px',
                            background: 'red',
                            color: '#fff',
                            borderRadius: '50%',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            cursor: 'pointer',
                            fontWeight: 'bold',
                            fontSize: '12px'
                        });
                                delImgBtn.addEventListener('click', async () => {
                                    // 从数据库中删除媒体文件
                                    const mediaId = item.images[imgIndex];
                                    if (mediaId.startsWith('media_')) {
                                        try {
                                            await deleteMediaFromDB(mediaId);
                                        } catch (error) {
                                            console.error(`[AI监控] 删除媒体文件 ${mediaId} 失败:`, error);
                                        }
                                    }

                                    // 从配置中移除
                            currentCategory.keywords[index].images.splice(imgIndex, 1);
                            renderKeywords();
                        });

                        imgWrapper.appendChild(delImgBtn);
                        imagesPreview.appendChild(imgWrapper);
                    });
                        } catch (error) {
                            console.error('[AI监控] 加载图片缩略图失败:', error);
                            imagesPreview.innerHTML = '<div style="color: red;">加载图片失败</div>';
                        }
                    })();
                }

                if (item.videos && item.videos.length > 0) {
                    const videosPreview = document.createElement('div');
                    videosPreview.style.display = 'flex';
                    videosPreview.style.flexWrap = 'wrap';
                    videosPreview.style.marginTop = '5px';

                    // 创建一个加载状态显示
                    const loadingDiv = document.createElement('div');
                    loadingDiv.textContent = '正在加载媒体文件...';
                    loadingDiv.style.padding = '5px';
                    loadingDiv.style.color = '#666';
                    videosPreview.appendChild(loadingDiv);
                    itemDiv.appendChild(videosPreview);

                    // 异步加载所有视频
                    (async () => {
                        try {
                            // 批量加载所有视频数据
                            const videoDataList = await loadMediaBatch(item.videos);

                            // 清除加载状态
                            videosPreview.innerHTML = '';

                            // 显示所有视频
                            videoDataList.forEach((vidData, vidIndex) => {
                                if (!vidData) {
                                    console.warn(`[AI监控] 视频 ${item.videos[vidIndex]} 加载失败`);
                                    return;
                                }

                        const vidWrapper = document.createElement('div');
                        vidWrapper.style.position = 'relative';
                        vidWrapper.style.marginRight = '5px';
                        vidWrapper.style.marginBottom = '5px';

                        const video = document.createElement('video');
                                video.src = vidData; // 使用加载的数据
                        video.style.width = '50px';
                        video.style.height = '50px';
                        video.style.objectFit = 'cover';
                        video.style.borderRadius = '5px';
                        video.controls = false;
                        vidWrapper.appendChild(video);

                        const delVidBtn = document.createElement('div');
                        delVidBtn.textContent = '×';
                        Object.assign(delVidBtn.style, {
                            position: 'absolute',
                            top: '0',
                            right: '0',
                            width: '15px',
                            height: '15px',
                            background: 'red',
                            color: '#fff',
                            borderRadius: '50%',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            cursor: 'pointer',
                            fontWeight: 'bold',
                            fontSize: '12px'
                        });
                                delVidBtn.addEventListener('click', async () => {
                                    // 从数据库中删除媒体文件
                                    const mediaId = item.videos[vidIndex];
                                    if (mediaId.startsWith('media_')) {
                                        try {
                                            await deleteMediaFromDB(mediaId);
                                        } catch (error) {
                                            console.error(`[AI监控] 删除媒体文件 ${mediaId} 失败:`, error);
                                        }
                                    }

                                    // 从配置中移除
                            currentCategory.keywords[index].videos.splice(vidIndex, 1);
                            renderKeywords();
                        });
                        vidWrapper.appendChild(delVidBtn);
                        videosPreview.appendChild(vidWrapper);
                    });
                        } catch (error) {
                            console.error('[AI监控] 加载视频缩略图失败:', error);
                            videosPreview.innerHTML = '<div style="color: red;">加载视频失败</div>';
                        }
                    })();
                }

                keywordItemsContainer.appendChild(itemDiv);
            });
        }
    }

    addKeywordBtn.addEventListener('click', () => {
        const currentCategory = aiConfig.categories[currentCategoryIndex];
        currentCategory.keywords.push({
            keyword: '',
            replyType: 'textWithFile', // 统一为新的回复类型
            text: '',
            images: [],
            videos: []
        });
        renderKeywords();
    });

    keywordListContainer.appendChild(addKeywordBtn);
    keywordListContainer.appendChild(keywordItemsContainer);
    configContainer.appendChild(keywordListContainer);

    /************************************************************
     * 4. 默认回复配置
     ************************************************************/
    const fallbackContainer = document.createElement('div');
    Object.assign(fallbackContainer.style, {
        border: '1px solid #ccc',
        borderRadius: '5px',
        padding: '10px',
        marginBottom: '10px'
    });

    const fallbackTitle = document.createElement('h4');
    fallbackTitle.textContent = '默认回复';
    fallbackTitle.style.margin = '0 0 10px 0';
    fallbackContainer.appendChild(fallbackTitle);

    // 随机默认回复开关
    const randomFallbackContainer = document.createElement('div');
    Object.assign(randomFallbackContainer.style, {
        display: 'flex',
        alignItems: 'center',
        marginBottom: '10px',
        gap: '8px'
    });

    const randomFallbackCheckbox = document.createElement('input');
    randomFallbackCheckbox.type = 'checkbox';
    randomFallbackCheckbox.id = 'randomFallbackCheckbox';
    randomFallbackCheckbox.checked = aiConfig.useRandomFallback || false;
    randomFallbackCheckbox.addEventListener('change', () => {
        aiConfig.useRandomFallback = randomFallbackCheckbox.checked;
        saveConfigToStorage();
    });

    const randomFallbackLabel = document.createElement('label');
    randomFallbackLabel.htmlFor = 'randomFallbackCheckbox';
    randomFallbackLabel.textContent = '随机默认回复';
    randomFallbackLabel.style.cursor = 'pointer';

    randomFallbackContainer.appendChild(randomFallbackCheckbox);
    randomFallbackContainer.appendChild(randomFallbackLabel);
    fallbackContainer.appendChild(randomFallbackContainer);

    // 用户过滤开关
    const userFilterContainer = document.createElement('div');
    Object.assign(userFilterContainer.style, {
        display: 'flex',
        alignItems: 'center',
        marginBottom: '10px',
        gap: '8px'
    });

    const userFilterCheckbox = document.createElement('input');
    userFilterCheckbox.type = 'checkbox';
    userFilterCheckbox.id = 'userFilterCheckbox';
    userFilterCheckbox.checked = aiConfig.enableUserFilter || false;
    userFilterCheckbox.addEventListener('change', () => {
        aiConfig.enableUserFilter = userFilterCheckbox.checked;
        saveConfigToStorage();
        // 显示/隐藏用户列表
        userListSection.style.display = aiConfig.enableUserFilter ? 'block' : 'none';
        showStatusMessage(
            aiConfig.enableUserFilter ? "已启用用户过滤功能" : "已关闭用户过滤功能",
            aiConfig.enableUserFilter ? "green" : "orange"
        );
    });

    const userFilterLabel = document.createElement('label');
    userFilterLabel.htmlFor = 'userFilterCheckbox';
    userFilterLabel.textContent = '判断用户';
    userFilterLabel.style.cursor = 'pointer';

    userFilterContainer.appendChild(userFilterCheckbox);
    userFilterContainer.appendChild(userFilterLabel);
    fallbackContainer.appendChild(userFilterContainer);

    // 用户列表管理区域
    const userListSection = document.createElement('div');
    Object.assign(userListSection.style, {
        border: '1px solid #e0e0e0',
        borderRadius: '4px',
        padding: '10px',
        marginBottom: '10px',
        backgroundColor: '#f9f9f9',
        display: aiConfig.enableUserFilter ? 'block' : 'none'
    });

    const userListTitle = document.createElement('h5');
    userListTitle.textContent = '允许默认回复的用户列表';
    userListTitle.style.margin = '0 0 10px 0';
    userListTitle.style.color = '#333';
    userListSection.appendChild(userListTitle);

    // 添加用户输入区域
    const addUserContainer = document.createElement('div');
    Object.assign(addUserContainer.style, {
        display: 'flex',
        gap: '8px',
        marginBottom: '10px'
    });

    const userInput = document.createElement('input');
    userInput.type = 'text';
    userInput.placeholder = '输入用户名（支持模糊匹配）';
    Object.assign(userInput.style, {
        flex: '1',
        padding: '6px',
        border: '1px solid #ddd',
        borderRadius: '4px'
    });

    const addUserBtn = document.createElement('button');
    addUserBtn.textContent = '+ 添加用户';
    Object.assign(addUserBtn.style, {
        backgroundColor: '#2196F3',
        color: 'white',
        border: 'none',
        padding: '6px 12px',
        borderRadius: '4px',
        cursor: 'pointer'
    });

    addUserContainer.appendChild(userInput);
    addUserContainer.appendChild(addUserBtn);
    userListSection.appendChild(addUserContainer);

    // 用户列表容器
    const userItemsContainer = document.createElement('div');
    Object.assign(userItemsContainer.style, {
        maxHeight: '150px',
        overflowY: 'auto',
        border: '1px solid #ddd',
        borderRadius: '4px',
        padding: '5px',
        backgroundColor: 'white'
    });

    // 渲染用户列表
    const renderUserList = () => {
        userItemsContainer.innerHTML = '';

        if (!aiConfig.allowedUsers || aiConfig.allowedUsers.length === 0) {
            const emptyMsg = document.createElement('div');
            emptyMsg.textContent = '暂无用户，添加用户后将只对列表中的用户启用默认回复';
            emptyMsg.style.color = '#999';
            emptyMsg.style.textAlign = 'center';
            emptyMsg.style.padding = '20px';
            userItemsContainer.appendChild(emptyMsg);
            return;
        }

        aiConfig.allowedUsers.forEach((username, index) => {
            const userItem = document.createElement('div');
            Object.assign(userItem.style, {
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                padding: '8px',
                marginBottom: '4px',
                backgroundColor: '#f5f5f5',
                borderRadius: '4px'
            });

            const userNameSpan = document.createElement('span');
            userNameSpan.textContent = username;
            userNameSpan.style.flex = '1';

            const deleteBtn = document.createElement('button');
            deleteBtn.textContent = '删除';
            Object.assign(deleteBtn.style, {
                backgroundColor: '#f44336',
                color: 'white',
                border: 'none',
                padding: '4px 8px',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '12px'
            });

            deleteBtn.addEventListener('click', () => {
                aiConfig.allowedUsers.splice(index, 1);
                saveConfigToStorage();
                renderUserList();
                showStatusMessage(`已删除用户: ${username}`, "orange");
            });

            userItem.appendChild(userNameSpan);
            userItem.appendChild(deleteBtn);
            userItemsContainer.appendChild(userItem);
        });
    };

    // 添加用户事件
    const addUser = () => {
        const username = userInput.value.trim();
        if (!username) {
            showStatusMessage("请输入用户名", "red");
            return;
        }

        if (aiConfig.allowedUsers.includes(username)) {
            showStatusMessage("用户已存在", "orange");
            return;
        }

        aiConfig.allowedUsers.push(username);
        saveConfigToStorage();
        renderUserList();
        userInput.value = '';
        showStatusMessage(`已添加用户: ${username}`, "green");
    };

    // 创建手动处理时间配置界面
    const createManualTimeoutConfig = () => {
        const timeoutContainer = document.createElement('div');
        Object.assign(timeoutContainer.style, {
            marginTop: '15px',
            padding: '10px',
            backgroundColor: '#f8f9fa',
            borderRadius: '6px',
            border: '1px solid #e9ecef'
        });

        const timeoutLabel = document.createElement('label');
        timeoutLabel.textContent = '手动处理时间阈值（秒）：';
        Object.assign(timeoutLabel.style, {
            display: 'block',
            marginBottom: '8px',
            fontWeight: 'bold',
            color: '#495057'
        });

        const timeoutInput = document.createElement('input');
        timeoutInput.type = 'number';
        timeoutInput.min = '10';
        timeoutInput.max = '300';
        timeoutInput.value = aiConfig.manualReplyTimeout || 30;
        Object.assign(timeoutInput.style, {
            width: '80px',
            padding: '4px 8px',
            border: '1px solid #ced4da',
            borderRadius: '4px',
            marginRight: '10px'
        });

        const timeoutDesc = document.createElement('span');
        timeoutDesc.textContent = '超过此时间后，白名单用户可以抢占处理';
        timeoutDesc.style.color = '#6c757d';
        timeoutDesc.style.fontSize = '12px';

        timeoutInput.addEventListener('change', () => {
            const value = parseInt(timeoutInput.value);
            if (value >= 10 && value <= 300) {
                aiConfig.manualReplyTimeout = value;
                saveConfigToStorage();
                showStatusMessage(`手动处理时间阈值已设置为 ${value} 秒`, "green");
            } else {
                timeoutInput.value = aiConfig.manualReplyTimeout || 30;
                showStatusMessage("时间阈值必须在10-300秒之间", "red");
            }
        });

        timeoutContainer.appendChild(timeoutLabel);
        timeoutContainer.appendChild(timeoutInput);
        timeoutContainer.appendChild(timeoutDesc);

        return timeoutContainer;
    };

    addUserBtn.addEventListener('click', addUser);
    userInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            addUser();
        }
    });

    userListSection.appendChild(userItemsContainer);

    // 添加手动处理时间配置
    const manualTimeoutConfig = createManualTimeoutConfig();
    userListSection.appendChild(manualTimeoutConfig);

    fallbackContainer.appendChild(userListSection);

    // 初始渲染用户列表
    renderUserList();

    // 默认回复列表容器
    const fallbackListContainer = document.createElement('div');
    fallbackListContainer.style.marginBottom = '10px';

    // 添加默认回复按钮
    const addFallbackBtn = document.createElement('button');
    addFallbackBtn.textContent = '+ 添加默认回复';
    Object.assign(addFallbackBtn.style, {
        backgroundColor: '#4CAF50',
        color: 'white',
        border: 'none',
        padding: '8px 16px',
        borderRadius: '4px',
        cursor: 'pointer',
        marginBottom: '10px'
    });

    // 默认回复项目容器
    const fallbackItemsContainer = document.createElement('div');
    fallbackItemsContainer.style.maxHeight = '200px';
    fallbackItemsContainer.style.overflowY = 'auto';

    // 渲染默认回复列表
    const renderFallbackReplies = () => {
        fallbackItemsContainer.innerHTML = '';

        if (!aiConfig.fallbackReplies || aiConfig.fallbackReplies.length === 0) {
            aiConfig.fallbackReplies = ["您好，请问有什么可以帮助您的？"];
        }

        aiConfig.fallbackReplies.forEach((reply, index) => {
            const itemDiv = document.createElement('div');
            Object.assign(itemDiv.style, {
                display: 'flex',
                alignItems: 'center',
                marginBottom: '8px',
                gap: '8px'
            });

            const input = document.createElement('input');
            input.type = 'text';
            input.value = reply;
            input.placeholder = '默认回复内容';
            Object.assign(input.style, {
                flex: '1',
                padding: '6px',
                border: '1px solid #ddd',
                borderRadius: '4px'
            });

            input.addEventListener('input', () => {
                aiConfig.fallbackReplies[index] = input.value.trim();
                // 更新旧的fallbackReply字段以保持兼容性
                if (index === 0) {
                    aiConfig.fallbackReply = input.value.trim();
                }
                saveConfigToStorage();
            });

            const deleteBtn = document.createElement('button');
            deleteBtn.textContent = '删除';
            Object.assign(deleteBtn.style, {
                backgroundColor: '#f44336',
                color: 'white',
                border: 'none',
                padding: '6px 12px',
                borderRadius: '4px',
                cursor: 'pointer'
            });

            deleteBtn.addEventListener('click', () => {
                if (aiConfig.fallbackReplies.length > 1) {
                    aiConfig.fallbackReplies.splice(index, 1);
                    // 更新旧的fallbackReply字段
                    aiConfig.fallbackReply = aiConfig.fallbackReplies[0] || "您好，请问有什么可以帮助您的？";
                    saveConfigToStorage();
                    renderFallbackReplies();
                } else {
                    alert('至少需要保留一个默认回复');
                }
            });

            itemDiv.appendChild(input);
            itemDiv.appendChild(deleteBtn);
            fallbackItemsContainer.appendChild(itemDiv);
        });
    };

    // 添加默认回复事件
    addFallbackBtn.addEventListener('click', () => {
        aiConfig.fallbackReplies.push('');
        saveConfigToStorage();
        renderFallbackReplies();
    });

    fallbackListContainer.appendChild(addFallbackBtn);
    fallbackListContainer.appendChild(fallbackItemsContainer);
    fallbackContainer.appendChild(fallbackListContainer);

    // 初始渲染
    renderFallbackReplies();

    configContainer.appendChild(fallbackContainer);

    /************************************************************
     * 5. AI接口配置区域
     ************************************************************/
    const aiSettingsContainer = document.createElement('div');
    Object.assign(aiSettingsContainer.style, {
        border: '1px solid #ccc',
        borderRadius: '5px',
        padding: '10px',
        marginBottom: '10px'
    });

    const aiSettingsTitle = document.createElement('h4');
    aiSettingsTitle.textContent = 'AI接口配置 (无关键词时使用AI回复)';
    aiSettingsTitle.style.margin = '0 0 10px 0';
    aiSettingsContainer.appendChild(aiSettingsTitle);

    const apiUrlInput = document.createElement('input');
    apiUrlInput.type = 'text';
    apiUrlInput.placeholder = 'API地址，例如：http://xxx.xx.xx.xx:8080/api';
    apiUrlInput.value = aiConfig.apiURL || '';
    Object.assign(apiUrlInput.style, {
        width: '100%',
        padding: '5px',
        marginBottom: '5px',
        boxSizing: 'border-box'
    });
    apiUrlInput.addEventListener('input', () => {
        aiConfig.apiURL = apiUrlInput.value.trim();
    });
    aiSettingsContainer.appendChild(apiUrlInput);

    const appIdInput = document.createElement('input');
    appIdInput.type = 'text';
    appIdInput.placeholder = '应用ID';
    appIdInput.value = aiConfig.applicationID || '';
    Object.assign(appIdInput.style, {
        width: '100%',
        padding: '5px',
        marginBottom: '5px',
        boxSizing: 'border-box'
    });
    appIdInput.addEventListener('input', () => {
        aiConfig.applicationID = appIdInput.value.trim();
    });
    aiSettingsContainer.appendChild(appIdInput);

    const appAuthInput = document.createElement('input');
    appAuthInput.type = 'text';
    appAuthInput.placeholder = '应用密钥(Authorization)';
    appAuthInput.value = aiConfig.appAuthorization || '';
    Object.assign(appAuthInput.style, {
        width: '100%',
        padding: '5px',
        marginBottom: '5px',
        boxSizing: 'border-box'
    });
    appAuthInput.addEventListener('input', () => {
        aiConfig.appAuthorization = appAuthInput.value.trim();
    });
    aiSettingsContainer.appendChild(appAuthInput);

    const useAIFallbackLabel = document.createElement('label');
    useAIFallbackLabel.style.display = 'inline-block';
    useAIFallbackLabel.style.marginRight = '10px';
    useAIFallbackLabel.textContent = '开启AI默认回复模式';
    aiSettingsContainer.appendChild(useAIFallbackLabel);

    const useAIFallbackCheckbox = document.createElement('input');
    useAIFallbackCheckbox.type = 'checkbox';
    useAIFallbackCheckbox.checked = aiConfig.useAIFallback;
    useAIFallbackCheckbox.addEventListener('change', () => {
        aiConfig.useAIFallback = useAIFallbackCheckbox.checked;
    });
    aiSettingsContainer.appendChild(useAIFallbackCheckbox);

    // 是否将触发词也发送给用户
    const hideTriggerWordsLabel = document.createElement('label');
    hideTriggerWordsLabel.style.display = 'block';
    hideTriggerWordsLabel.style.marginTop = '10px';
    hideTriggerWordsLabel.textContent = '是否将带方括号的触发词也发送给用户？(默认否)';
    aiSettingsContainer.appendChild(hideTriggerWordsLabel);

    const hideTriggerWordsCheckbox = document.createElement('input');
    hideTriggerWordsCheckbox.type = 'checkbox';
    hideTriggerWordsCheckbox.checked = aiConfig.sendTriggerWordsToUser;
    hideTriggerWordsCheckbox.addEventListener('change', () => {
        aiConfig.sendTriggerWordsToUser = hideTriggerWordsCheckbox.checked;
    });
    aiSettingsContainer.appendChild(hideTriggerWordsCheckbox);

    configContainer.appendChild(aiSettingsContainer);

    /************************************************************
     * 6. 底部按钮行
     ************************************************************/
    const bottomBtnContainer = document.createElement('div');
    bottomBtnContainer.style.display = 'flex';
    bottomBtnContainer.style.justifyContent = 'space-between';
    bottomBtnContainer.style.alignItems = 'center';

    // 重置当前分组
    const resetBtn = document.createElement('button');
    resetBtn.textContent = '重置当前分组';
    Object.assign(resetBtn.style, {
        backgroundColor: 'orange',
        color: '#fff',
        border: 'none',
        borderRadius: '5px',
        padding: '5px 10px',
        cursor: 'pointer'
    });
    resetBtn.addEventListener('click', () => {
        if (confirm('确认清空当前分组的关键词回复列表吗？此操作不可撤销。')) {
            aiConfig.categories[currentCategoryIndex].keywords = [];
            renderKeywords();
        }
    });
    bottomBtnContainer.appendChild(resetBtn);

    // 保存配置
    const saveBtn = document.createElement('button');
    saveBtn.textContent = '保存配置';
    Object.assign(saveBtn.style, {
        background: '#1966ff',
        color: '#fff',
        border: 'none',
        borderRadius: '5px',
        padding: '5px 10px',
        cursor: 'pointer'
    });
    saveBtn.addEventListener('click', () => {
        saveConfigToStorage();
            showStatusMessage("配置已保存", "green");
    });
    bottomBtnContainer.appendChild(saveBtn);

    // 导出配置
    const exportBtn = document.createElement('button');
    exportBtn.textContent = '导出配置';
    Object.assign(exportBtn.style, {
        backgroundColor: '#17a2b8',
        color: '#fff',
        border: 'none',
        borderRadius: '5px',
        padding: '5px 10px',
        cursor: 'pointer',
        marginRight: '10px'
    });
    exportBtn.addEventListener('click', async () => {
        try {
            // 准备要导出的配置
            const exportConfig = JSON.parse(JSON.stringify(aiConfig));

            // 导出选项容器
            const exportOptionsWindow = document.createElement('div');
            Object.assign(exportOptionsWindow.style, {
                position: 'fixed',
                top: '0',
                left: '0',
                width: '100%',
                height: '100%',
                backgroundColor: 'rgba(0,0,0,0.5)',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                zIndex: 10000
            });

            const exportOptionsBox = document.createElement('div');
            Object.assign(exportOptionsBox.style, {
                width: '500px',
                backgroundColor: '#fff',
                borderRadius: '5px',
                padding: '20px',
                boxShadow: '0 0 10px rgba(0,0,0,0.2)'
            });

            const exportOptionsTitle = document.createElement('h3');
            exportOptionsTitle.textContent = '导出选项';
            exportOptionsTitle.style.marginTop = '0';
            exportOptionsBox.appendChild(exportOptionsTitle);

            // 添加导出选项
            const includeMediaOption = document.createElement('div');
            includeMediaOption.style.marginBottom = '15px';

            const includeMediaCheckbox = document.createElement('input');
            includeMediaCheckbox.type = 'checkbox';
            includeMediaCheckbox.id = 'includeMedia';
            includeMediaCheckbox.checked = true;

            const includeMediaLabel = document.createElement('label');
            includeMediaLabel.htmlFor = 'includeMedia';
            includeMediaLabel.textContent = '包含媒体文件（图片和视频）';
            includeMediaLabel.style.marginLeft = '5px';

            includeMediaOption.appendChild(includeMediaCheckbox);
            includeMediaOption.appendChild(includeMediaLabel);
            exportOptionsBox.appendChild(includeMediaOption);

            // 媒体文件大小警告
            const mediaWarning = document.createElement('div');
            mediaWarning.style.color = 'orange';
            mediaWarning.style.marginBottom = '15px';
            mediaWarning.textContent = '警告：包含媒体文件可能会导致配置文件非常大，不建议在自动导入场景使用。';
            exportOptionsBox.appendChild(mediaWarning);

            // 按钮容器
            const btnContainer = document.createElement('div');
            Object.assign(btnContainer.style, {
                display: 'flex',
                justifyContent: 'flex-end',
                marginTop: '20px'
            });

            // 取消按钮
            const cancelBtn = document.createElement('button');
            cancelBtn.textContent = '取消';
            Object.assign(cancelBtn.style, {
                backgroundColor: '#6c757d',
                color: '#fff',
                border: 'none',
                borderRadius: '5px',
                padding: '5px 15px',
                cursor: 'pointer',
                marginRight: '10px'
            });
            cancelBtn.addEventListener('click', () => {
                document.body.removeChild(exportOptionsWindow);
            });
            btnContainer.appendChild(cancelBtn);

            // 确认导出按钮
            const confirmExportBtn = document.createElement('button');
            confirmExportBtn.textContent = '导出';
            Object.assign(confirmExportBtn.style, {
                backgroundColor: '#28a745',
                color: '#fff',
                border: 'none',
                borderRadius: '5px',
                padding: '5px 15px',
                cursor: 'pointer'
            });

            confirmExportBtn.addEventListener('click', async () => {
                // 显示加载状态
                confirmExportBtn.textContent = '处理中...';
                confirmExportBtn.disabled = true;

                try {
                    // 根据选项处理媒体文件
                    if (includeMediaCheckbox.checked) {
                        // 需要包含媒体文件，将ID替换为实际数据
                        for (const category of exportConfig.categories) {
                            for (const keyword of category.keywords) {
                                // 处理图片
                                if (keyword.images && keyword.images.length > 0) {
                                    const imagePromises = keyword.images.map(async (imageId) => {
                                        return await loadMediaFromDB(imageId);
                                    });
                                    keyword.images = await Promise.all(imagePromises);
                                    keyword.images = keyword.images.filter(img => img !== null);
                                }

                                // 处理视频
                                if (keyword.videos && keyword.videos.length > 0) {
                                    const videoPromises = keyword.videos.map(async (videoId) => {
                                        return await loadMediaFromDB(videoId);
                                    });
                                    keyword.videos = await Promise.all(videoPromises);
                                    keyword.videos = keyword.videos.filter(vid => vid !== null);
                                }
                            }
                        }

                        console.log("[AI监控] 导出配置(包含媒体文件)");
                    } else {
                        // 不包含媒体文件，则删除所有媒体数据
                        for (const category of exportConfig.categories) {
                            for (const keyword of category.keywords) {
                                if (keyword.images) keyword.images = [];
                                if (keyword.videos) keyword.videos = [];
                            }
                        }
                        console.log("[AI监控] 导出配置(不含媒体文件)");
                    }

                    // 导出配置
                    const exportData = JSON.stringify(exportConfig, null, 2);

                    // 创建导出窗口
            const exportWindow = document.createElement('div');
            Object.assign(exportWindow.style, {
                position: 'fixed',
                top: '0',
                left: '0',
                width: '100%',
                height: '100%',
                backgroundColor: 'rgba(0,0,0,0.5)',
                display: 'flex',
                justifyContent: 'center',
                        alignItems: 'center',
                zIndex: 10000
            });

            const contentDiv = document.createElement('div');
            Object.assign(contentDiv.style, {
                background: '#fff',
                padding: '20px',
                borderRadius: '10px',
                boxShadow: '0 0 10px rgba(0,0,0,0.3)',
                        maxWidth: '80%',
                        width: '100%',
                        maxHeight: '80vh',
                        overflow: 'auto'
            });

            const textarea = document.createElement('textarea');
            textarea.style.width = '100%';
                    textarea.style.height = '300px';
                    textarea.style.marginBottom = '10px';
                    textarea.style.fontFamily = 'monospace';
                    textarea.value = exportData;
            contentDiv.appendChild(textarea);

            const closeBtn = document.createElement('button');
            closeBtn.textContent = '关闭';
            Object.assign(closeBtn.style, {
                marginTop: '10px',
                        backgroundColor: '#dc3545',
                color: '#fff',
                border: 'none',
                borderRadius: '5px',
                padding: '5px 10px',
                cursor: 'pointer'
            });
            closeBtn.addEventListener('click', () => {
                document.body.removeChild(exportWindow);
            });
            contentDiv.appendChild(closeBtn);

            exportWindow.appendChild(contentDiv);
            document.body.appendChild(exportWindow);

                    // 关闭导出选项窗口
                    document.body.removeChild(exportOptionsWindow);
                } catch (error) {
                    console.error('[AI监控] 导出配置失败:', error);
                    alert('导出失败: ' + error.message);
                    confirmExportBtn.textContent = '导出';
                    confirmExportBtn.disabled = false;
                }
            });

            btnContainer.appendChild(confirmExportBtn);
            exportOptionsBox.appendChild(btnContainer);

            exportOptionsWindow.appendChild(exportOptionsBox);
            document.body.appendChild(exportOptionsWindow);
        } catch (e) {
            aiLogger.error(`[AI监控] 导出配置时出错：${e.message}`);
            showStatusMessage("导出配置失败", "red");
        }
    });

    // 导入配置
    const importBtn = document.createElement('button');
    importBtn.textContent = '导入配置';
    Object.assign(importBtn.style, {
        backgroundColor: '#6c757d',
        color: '#fff',
        border: 'none',
        borderRadius: '5px',
        padding: '5px 10px',
        cursor: 'pointer',
        marginRight: '10px'
    });
    importBtn.addEventListener('click', () => {
        const importWindow = document.createElement('div');
        Object.assign(importWindow.style, {
            position: 'fixed',
            top: '0',
            left: '0',
            width: '100%',
            height: '100%',
            backgroundColor: 'rgba(0,0,0,0.5)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 10000
        });

        const contentDiv = document.createElement('div');
        Object.assign(contentDiv.style, {
            background: '#fff',
            padding: '20px',
            borderRadius: '10px',
            boxShadow: '0 0 10px rgba(0,0,0,0.3)',
            maxWidth: '500px',
            width: '100%'
        });

        const descP = document.createElement('p');
        descP.textContent = '请在下方粘贴导出的配置JSON，然后点击导入：';
        contentDiv.appendChild(descP);

        const textarea = document.createElement('textarea');
        textarea.style.width = '100%';
        textarea.style.height = '200px';
        contentDiv.appendChild(textarea);

        const btnContainer = document.createElement('div');
        btnContainer.style.marginTop = '10px';
        btnContainer.style.textAlign = 'right';

        const confirmImportBtn = document.createElement('button');
        confirmImportBtn.textContent = '导入';
        Object.assign(confirmImportBtn.style, {
            backgroundColor: '#28a745',
            color: '#fff',
            border: 'none',
            borderRadius: '5px',
            padding: '5px 10px',
            cursor: 'pointer',
            marginRight: '10px'
        });
        confirmImportBtn.addEventListener('click', async () => {
            const jsonStr = textarea.value.trim();
            if (!jsonStr) {
                alert('请输入配置JSON');
                return;
            }

            // 显示处理中状态
            confirmImportBtn.textContent = '处理中...';
            confirmImportBtn.disabled = true;

            try {
                const parsed = JSON.parse(jsonStr);
                if (typeof parsed === 'object' && parsed.categories && parsed.fallbackReply !== undefined) {
                    // 处理可能包含的媒体文件，将其存储到IndexedDB
                    for (const category of parsed.categories) {
                        for (const keyword of category.keywords) {
                            // 处理图片
                            if (keyword.images && keyword.images.length > 0) {
                                const newImageIds = [];
                                for (const imageData of keyword.images) {
                                    // 跳过已经是ID格式的媒体文件引用
                                    if (typeof imageData === 'string' && imageData.startsWith('media_')) {
                                        newImageIds.push(imageData);
                                        continue;
                                    }

                                    try {
                                        // 保存到IndexedDB并获取ID
                                        const mediaId = await saveMediaToDB(imageData, 'image');
                                        newImageIds.push(mediaId);
                                    } catch (error) {
                                        console.error('[AI监控] 导入图片保存失败:', error);
                                        // 如果无法保存到IndexedDB，保留原始数据
                                        newImageIds.push(imageData);
                                    }
                                }
                                keyword.images = newImageIds;
                            }

                            // 处理视频
                            if (keyword.videos && keyword.videos.length > 0) {
                                const newVideoIds = [];
                                for (const videoData of keyword.videos) {
                                    // 跳过已经是ID格式的媒体文件引用
                                    if (typeof videoData === 'string' && videoData.startsWith('media_')) {
                                        newVideoIds.push(videoData);
                                        continue;
                                    }

                                    try {
                                        // 保存到IndexedDB并获取ID
                                        const mediaId = await saveMediaToDB(videoData, 'video');
                                        newVideoIds.push(mediaId);
                                    } catch (error) {
                                        console.error('[AI监控] 导入视频保存失败:', error);
                                        // 如果无法保存到IndexedDB，保留原始数据
                                        newVideoIds.push(videoData);
                                    }
                                }
                                keyword.videos = newVideoIds;
                            }
                        }
                    }

                    // 更新配置并保存
                    aiConfig = parsed;
                    saveConfigToStorage();

                    // 更新UI
                    renderCategoryNav();
                    renderKeywords();
                    renderFallbackReplies(); // 更新默认回复列表
                    renderUserList(); // 更新用户列表
                    randomFallbackCheckbox.checked = aiConfig.useRandomFallback || false;
                    userFilterCheckbox.checked = aiConfig.enableUserFilter || false;
                    userListSection.style.display = aiConfig.enableUserFilter ? 'block' : 'none';
                    autoCloseCheckbox.checked = aiConfig.autoCloseSession || false;
                    apiUrlInput.value = aiConfig.apiURL || '';
                    appIdInput.value = aiConfig.applicationID || '';
                    appAuthInput.value = aiConfig.appAuthorization || '';
                    useAIFallbackCheckbox.checked = aiConfig.useAIFallback || false;
                    sendTriggerWordsCheckbox.checked = aiConfig.sendTriggerWordsToUser || false;

                    document.body.removeChild(importWindow);
                    showStatusMessage("配置已导入并保存", "green");
                } else {
                    alert('无效的配置格式');
                    confirmImportBtn.textContent = '导入';
                    confirmImportBtn.disabled = false;
                }
            } catch (e) {
                alert('解析配置时出错: ' + e.message);
                confirmImportBtn.textContent = '导入';
                confirmImportBtn.disabled = false;
            }
        });
        btnContainer.appendChild(confirmImportBtn);

        contentDiv.appendChild(btnContainer);
        importWindow.appendChild(contentDiv);
        document.body.appendChild(importWindow);
    });
    bottomBtnContainer.appendChild(importBtn);

    configContainer.appendChild(bottomBtnContainer);

    // 配置面板插入页面
    document.body.appendChild(configContainer);

    /************************************************************
     * 7. 额外按钮：查看特殊按钮触发词
     ************************************************************/
    const showSpecialTriggersBtn = document.createElement('button');
    showSpecialTriggersBtn.textContent = '查看特殊按钮触发词';
    Object.assign(showSpecialTriggersBtn.style, {
        position: 'absolute',
        top: '10px',
        right: '10px',
        zIndex: 9999,
        backgroundColor: '#444',
        color: '#fff',
        border: 'none',
        borderRadius: '5px',
        padding: '5px 10px',
        cursor: 'pointer',
        fontSize: '12px'
    });
    showSpecialTriggersBtn.addEventListener('click', () => {
        showSpecialTriggersOverlay();
    });
    configContainer.appendChild(showSpecialTriggersBtn);

    function showSpecialTriggersOverlay() {
        const overlay = document.createElement('div');
        Object.assign(overlay.style, {
            position: 'fixed',
            top: '0',
            left: '0',
            width: '100%',
            height: '100%',
            backgroundColor: 'rgba(0,0,0,0.5)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 10000
        });

        const content = document.createElement('div');
        Object.assign(content.style, {
            background: '#fff',
            padding: '20px',
            borderRadius: '10px',
            boxShadow: '0 0 10px rgba(0,0,0,0.3)',
            minWidth: '300px'
        });

        const title = document.createElement('h3');
        title.textContent = '特殊客服回复触发词';
        content.appendChild(title);

        specialSendTriggers.forEach(item => {
            const p = document.createElement('p');
            p.textContent = `当自动回复文本中包含 "${item.triggerText}" → 点击第 ${item.btnIndex + 1} 个订单"发送"按钮`;
            content.appendChild(p);
        });

        const tip = document.createElement('p');
        tip.style.color = 'red';
        tip.textContent = '注意：若"是否将触发词也发送给用户"未勾选，则不会真正发给用户，但仍能触发按钮点击。';
        content.appendChild(tip);

        const closeBtn = document.createElement('button');
        closeBtn.textContent = '关闭';
        Object.assign(closeBtn.style, {
            backgroundColor: '#1966ff',
            color: '#fff',
            border: 'none',
            borderRadius: '5px',
            padding: '5px 10px',
            cursor: 'pointer'
        });
        closeBtn.addEventListener('click', () => {
            document.body.removeChild(overlay);
        });
        content.appendChild(closeBtn);

        overlay.appendChild(content);
        document.body.appendChild(overlay);
    }

    // 渲染初始分类与关键词
    renderCategoryNav();
    renderKeywords();

    /************************************************************
     * 8. 自动回复核心逻辑
     ************************************************************/
    function getMatchedCategoryForOrder(orderInfo) {
        if (!orderInfo || orderInfo.noOrders || !orderInfo.orders || orderInfo.orders.length === 0) {
            return aiConfig.categories[0];
        }
        const allNames = orderInfo.orders.map(o => o.name).join(" ");

        // 遍历分类组，查找匹配的分类组
        for (let i = 0; i < aiConfig.categories.length; i++) {
            // 跳过默认分类
            if (aiConfig.categories[i].isDefault) continue;
            // 如果匹配，则使用该分类组
            if (allNames.includes(aiConfig.categories[i].keyword)) {
                return aiConfig.categories[i];
            }
        }
        // 如果没有匹配到，返回默认分类组
        return aiConfig.categories.find(c => c.isDefault) || aiConfig.categories[0];
    }

    // 检查用户是否允许接收默认回复
    const isUserAllowedForFallback = (username) => {
        // 如果没有启用用户过滤，所有用户都允许
        if (!aiConfig.enableUserFilter) {
            return true;
        }

        // 如果用户列表为空，不允许任何用户
        if (!aiConfig.allowedUsers || aiConfig.allowedUsers.length === 0) {
            return false;
        }

        // 检查用户名是否在允许列表中（支持模糊匹配）
        return aiConfig.allowedUsers.some(allowedUser => {
            // 精确匹配
            if (username === allowedUser) {
                return true;
            }
            // 模糊匹配：用户名包含允许的用户名
            if (username.includes(allowedUser) || allowedUser.includes(username)) {
                return true;
            }
            return false;
        });
    };

    // 检查是否应该中断手动处理（混合策略核心逻辑）
    const shouldInterruptManualReply = () => {
        if (!isManualReplying) {
            return true; // 没在手动处理，可以处理白名单用户
        }

        if (!manualReplyStartTime) {
            return true; // 没有开始时间记录，允许处理
        }

        const processingTime = Date.now() - manualReplyStartTime;
        const timeoutMs = (aiConfig.manualReplyTimeout || 30) * 1000;

        const canInterrupt = processingTime >= timeoutMs;
        if (canInterrupt) {
            aiLogger.info(`[混合策略] 手动处理已超时 ${Math.round(processingTime/1000)}秒，白名单用户可以抢占`);
        }

        return canInterrupt;
    };

    // 添加用户到相应队列
    const addUserToQueue = (username, isWhitelist) => {
        if (isWhitelist) {
            if (!whitelistUserQueue.includes(username)) {
                whitelistUserQueue.push(username);
                aiLogger.info(`[混合策略] 白名单用户 ${username} 加入优先队列`);
            }
        } else {
            if (!nonWhitelistUserQueue.includes(username)) {
                nonWhitelistUserQueue.push(username);
                aiLogger.info(`[混合策略] 非白名单用户 ${username} 加入等待队列`);
            }
        }
    };

    // 从队列中移除用户
    const removeUserFromQueue = (username) => {
        const whitelistIndex = whitelistUserQueue.indexOf(username);
        if (whitelistIndex > -1) {
            whitelistUserQueue.splice(whitelistIndex, 1);
            aiLogger.info(`[混合策略] 白名单用户 ${username} 已从队列移除`);
        }

        const nonWhitelistIndex = nonWhitelistUserQueue.indexOf(username);
        if (nonWhitelistIndex > -1) {
            nonWhitelistUserQueue.splice(nonWhitelistIndex, 1);
            aiLogger.info(`[混合策略] 非白名单用户 ${username} 已从队列移除`);
        }
    };

    // 获取默认回复（支持随机选择和用户过滤）
    const getFallbackReply = (username = null) => {
        // 如果启用了用户过滤且用户不在允许列表中，返回null表示不回复
        if (username && !isUserAllowedForFallback(username)) {
            aiLogger.info(`[用户过滤] 用户 ${username} 不在允许列表中，跳过默认回复`);
            return null;
        }

        if (!aiConfig.fallbackReplies || aiConfig.fallbackReplies.length === 0) {
            return aiConfig.fallbackReply || "您好，请问有什么可以帮助您的？";
        }

        if (aiConfig.useRandomFallback && aiConfig.fallbackReplies.length > 1) {
            // 随机选择一个默认回复
            const randomIndex = Math.floor(Math.random() * aiConfig.fallbackReplies.length);
            return aiConfig.fallbackReplies[randomIndex];
        } else {
            // 使用第一个默认回复
            return aiConfig.fallbackReplies[0] || aiConfig.fallbackReply || "您好，请问有什么可以帮助您的？";
        }
    };

    const getAutoReplyMessage = (lastUserMessage, matchedKeywordConfig, username = null) => {
        if (!matchedKeywordConfig) return getFallbackReply(username);

        // 简化处理逻辑，统一构建回复对象
        const result = {
            text: matchedKeywordConfig.text || '',
            images: matchedKeywordConfig.images || [],
            videos: matchedKeywordConfig.videos || []
        };

        // 检查是否只有文本（没有图片和视频）
        if (result.images.length === 0 && result.videos.length === 0) {
            // 如果只有文本，直接返回文本字符串
            return result.text || getFallbackReply(username);
        }

        // 否则返回完整对象
        return result;
    };

    let isReplying = false;
    let isClosingSession = false; // 新增：会话关闭状态锁，防止时序冲突

    // 新增：混合策略优先级处理相关状态
    let isManualReplying = false; // 是否正在手动处理非白名单用户
    let manualReplyStartTime = null; // 手动处理开始时间
    let whitelistUserQueue = []; // 白名单用户队列
    let nonWhitelistUserQueue = []; // 非白名单用户队列

    const autoReplyToWaitReplyUsers = () => {
        // 检查是否正在回复或关闭会话，如果是则暂停处理
        if (isReplying || isClosingSession) {
            if (isClosingSession) {
                aiLogger.info("[监控暂停] 正在关闭会话中，暂停处理新用户");
            }
            return;
        }

        let usersToReply = {};
        statusesToAutoReply.forEach(statusKey => {
            if (lastStatusMap[statusKey] && lastStatusMap[statusKey].users) {
                Object.assign(usersToReply, lastStatusMap[statusKey].users);
            }
        });

        const usernames = Object.keys(usersToReply);
        if (usernames.length === 0) {
            // 没有新用户，但检查是否有排队的用户需要处理
            processQueuedUsers();
            return;
        }

        // 将用户分类到不同队列
        usernames.forEach(username => {
            const isWhitelist = isUserAllowedForFallback(username);
            addUserToQueue(username, isWhitelist);
        });

        // 处理队列中的用户
        processQueuedUsers();
    };

    // 处理队列中的用户（混合策略核心）
    const processQueuedUsers = () => {
        // 优先处理白名单用户
        if (whitelistUserQueue.length > 0) {
            // 检查是否可以中断手动处理
            if (shouldInterruptManualReply()) {
                if (isManualReplying) {
                    aiLogger.info("[混合策略] 中断手动处理，优先处理白名单用户");
                    isManualReplying = false;
                    manualReplyStartTime = null;
                }

                const username = whitelistUserQueue[0];
                processWhitelistUser(username);
                return;
            } else {
                aiLogger.info(`[混合策略] 白名单用户排队等待，手动处理进行中 (${Math.round((Date.now() - manualReplyStartTime)/1000)}秒)`);
                return;
            }
        }

        // 处理非白名单用户（需要手动处理）
        if (nonWhitelistUserQueue.length > 0 && !isManualReplying) {
            const username = nonWhitelistUserQueue[0];
            processNonWhitelistUser(username);
            return;
        }
    };

    // 处理白名单用户（自动回复）
    const processWhitelistUser = (username) => {
        aiLogger.info(`[混合策略] 开始处理白名单用户: ${username}`);

        // 移除当前用户并继续处理原有逻辑
        removeUserFromQueue(username);

        // 调用原有的自动回复逻辑
        processUserAutoReply(username, true);
    };

    // 处理非白名单用户（手动处理）
    const processNonWhitelistUser = (username) => {
        aiLogger.info(`[混合策略] 检测到非白名单用户: ${username}，需要手动处理`);

        // 设置手动处理状态
        isManualReplying = true;
        manualReplyStartTime = Date.now();

        // 切换到该用户（如果不是当前选中用户）
        if (lastSelectedUser !== username) {
            clickUserInWaitReply(username, () => {
                aiLogger.info(`[混合策略] 已切换到非白名单用户: ${username}，请手动回复`);
                showManualReplyNotification(username);
            });
        } else {
            aiLogger.info(`[混合策略] 非白名单用户 ${username} 已在当前界面，请手动回复`);
            showManualReplyNotification(username);
        }
    };

    // 显示手动回复提醒
    const showManualReplyNotification = (username) => {
        const timeoutSeconds = aiConfig.manualReplyTimeout || 30;
        aiLogger.info(`[混合策略] 请手动回复用户: ${username}，${timeoutSeconds}秒后白名单用户可抢占`);

        // 可以在这里添加更明显的UI提醒，比如浏览器通知
        if (Notification.permission === "granted") {
            new Notification("需要手动回复", {
                body: `用户 ${username} 需要手动回复，${timeoutSeconds}秒后白名单用户可抢占`,
                icon: "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'><path fill='%23ff6b6b' d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z'/></svg>"
            });
        }
    };

    // 原有的用户自动回复逻辑（重构为独立函数）
    const processUserAutoReply = (uname, isWhitelistUser = false) => {
        if (uname === lastSelectedUser && lastChatMessages && lastChatMessages.length > 0) {
            // 当前选中用户
            const userMessages = lastChatMessages.filter(m => m.sender === '用户');
            if (userMessages.length === 0) return;

            const lastUserMessageObj = userMessages[userMessages.length - 1];
            const lastUserMsg = lastUserMessageObj.content;
            const lastUserMsgTime = lastUserMessageObj.time || "";

            const lastReplied = repliedMessagesMap[uname];
            if (lastReplied && lastReplied.text === lastUserMsg && lastReplied.time === lastUserMsgTime) {
                // 已经回复过这句话
                return;
            }

                const category = getMatchedCategoryForOrder(lastOrderInfo);
                let matchedConfig = null;
                for (const kw of category.keywords) {
                    if (kw.keyword && lastUserMsg.includes(kw.keyword)) {
                        matchedConfig = kw;
                        break;
                    }
                }

                if (!matchedConfig) {
                    // 无关键词匹配 → AI
                    if (aiConfig.useAIFallback && aiConfig.apiURL && aiConfig.applicationID && aiConfig.appAuthorization) {
                        isReplying = true;
                        getAIResponse(lastUserMsg, lastChatMessages, lastOrderInfo).then(responseText => {
                            const replyMsg = responseText || getFallbackReply(uname);
                            if (replyMsg) {
                                sendMessageToUser(replyMsg, () => {
                                    repliedMessagesMap[uname] = { text: lastUserMsg, time: lastUserMsgTime };
                                    isReplying = false;
                                }, uname);
                            } else {
                                // 用户被过滤，不回复
                                isReplying = false;
                            }
                        }).catch(err => {
                            aiLogger.error("[AI监控] AI接口调用失败：" + err.message);
                            isReplying = true;
                            const fallbackMsg = getFallbackReply(uname);
                            if (fallbackMsg) {
                                sendMessageToUser(fallbackMsg, () => {
                                    repliedMessagesMap[uname] = { text: lastUserMsg, time: lastUserMsgTime };
                                    isReplying = false;
                                }, uname);
                            } else {
                                // 用户被过滤，不回复
                                isReplying = false;
                            }
                        });
                        break;
                    } else {
                        // 检查是否允许默认回复
                        const fallbackMsg = getFallbackReply(uname);
                        if (fallbackMsg) {
                            matchedConfig = {
                                replyType: 'text',
                                text: fallbackMsg
                            };
                        } else {
                            // 用户被过滤，跳过此用户
                            continue;
                        }
                    }
                }

                if (matchedConfig) {
                    const replyMsg = getAutoReplyMessage(lastUserMsg, matchedConfig, uname);
                    if (!replyMsg) continue;

                    isReplying = true;
                    sendMessageToUser(replyMsg, () => {
                        repliedMessagesMap[uname] = { text: lastUserMsg, time: lastUserMsgTime };
                        isReplying = false;
                    }, uname);
                }
                break;
            } else {
                // 若不是当前选中用户，需要先点进去，获取消息再回复
                isReplying = true;
                clickUserInWaitReply(uname, () => {
                    const checkInterval = setInterval(() => {
                        if (lastSelectedUser === uname && lastChatMessages && lastChatMessages.length > 0) {
                            clearInterval(checkInterval);
                            const userMessages = lastChatMessages.filter(m => m.sender === '用户');
                            if (userMessages.length === 0) {
                                isReplying = false;
                                return;
                            }
                            const lastUserMessageObj = userMessages[userMessages.length - 1];
                            const lastUserMsg = lastUserMessageObj.content;
                            const lastUserMsgTime = lastUserMessageObj.time || "";

                            const lastReplied = repliedMessagesMap[uname];
                            if (lastReplied && lastReplied.text === lastUserMsg && lastReplied.time === lastUserMsgTime) {
                                isReplying = false;
                                return;
                            }

                            const category = getMatchedCategoryForOrder(lastOrderInfo);
                            let matchedConfig = null;
                            for (const kw of category.keywords) {
                                if (kw.keyword && lastUserMsg.includes(kw.keyword)) {
                                    matchedConfig = kw;
                                    break;
                                }
                            }

                            if (!matchedConfig) {
                                // 无关键词 → AI
                                if (aiConfig.useAIFallback && aiConfig.apiURL && aiConfig.applicationID && aiConfig.appAuthorization) {
                                    getAIResponse(lastUserMsg, lastChatMessages, lastOrderInfo).then(responseText => {
                                        const replyMsg = responseText || getFallbackReply(uname);
                                        if (replyMsg) {
                                            sendMessageToUser(replyMsg, () => {
                                                repliedMessagesMap[uname] = { text: lastUserMsg, time: lastUserMsgTime };
                                                isReplying = false;
                                            }, uname);
                                        } else {
                                            // 用户被过滤，不回复
                                            isReplying = false;
                                        }
                                    }).catch(err => {
                                        aiLogger.error("[AI监控] AI接口调用失败：" + err.message);
                                        const fallbackMsg = getFallbackReply(uname);
                                        if (fallbackMsg) {
                                            sendMessageToUser(fallbackMsg, () => {
                                                repliedMessagesMap[uname] = { text: lastUserMsg, time: lastUserMsgTime };
                                                isReplying = false;
                                            }, uname);
                                        } else {
                                            // 用户被过滤，不回复
                                            isReplying = false;
                                        }
                                    });
                                } else {
                                    // 检查是否允许默认回复
                                    const fallbackMsg = getFallbackReply(uname);
                                    if (fallbackMsg) {
                                        matchedConfig = {
                                            replyType: 'text',
                                            text: fallbackMsg
                                        };
                                        const replyMsg = getAutoReplyMessage(lastUserMsg, matchedConfig, uname);
                                        sendMessageToUser(replyMsg, () => {
                                            repliedMessagesMap[uname] = { text: lastUserMsg, time: lastUserMsgTime };
                                            isReplying = false;
                                        }, uname);
                                    } else {
                                        // 用户被过滤，不回复
                                        isReplying = false;
                                    }
                                }
                            } else {
                                const replyMsg = getAutoReplyMessage(lastUserMsg, matchedConfig, uname);
                                sendMessageToUser(replyMsg, () => {
                                    repliedMessagesMap[uname] = { text: lastUserMsg, time: lastUserMsgTime };
                                    isReplying = false;
                                }, uname);
                            }
                        }
                    }, 500);
                });
                break;
            }
        }
    };

    const clickUserInWaitReply = (username, callback) => {
        const chatArea = document.querySelector("#chantListScrollArea");
        if (!chatArea) {
            callback();
            return;
        }
        // 更新选择器：先尝试新的结构，再尝试旧的结构
        let chatListContainer = chatArea.querySelector(".list_items");
        if (!chatListContainer) {
            chatListContainer = chatArea.querySelector("[class*='ReactVirtualized__Grid__innerScrollContainer']");
        }
        if (!chatListContainer) {
            callback();
            return;
        }
        const userItems = chatListContainer.querySelectorAll('[data-btm-id]');
        for (const item of userItems) {
            const { username: uname } = getUsernameAndMessage(item);
            if (uname === username) {
                item.click();
                setTimeout(() => {
                    callback();
                }, 500);
                break;
            }
        }
    };

    /************************************************************
     * 9. 自动关闭会话功能（增强版 - 支持弹窗处理）
     ************************************************************/

    // 检测并处理关闭会话确认弹窗
    function handleCloseSessionModal() {
        return new Promise((resolve) => {
            // 查找确认弹窗
            const modal = document.querySelector('.auxo-modal.auxo-modal-confirm');
            if (!modal || modal.offsetParent === null) {
                resolve(null); // 没有弹窗
                return;
            }

            // 检查弹窗内容
            const titleElement = modal.querySelector('.auxo-modal-confirm-title');
            const contentElement = modal.querySelector('.auxo-modal-confirm-content');

            if (!titleElement || !contentElement) {
                resolve(null);
                return;
            }

            const title = titleElement.textContent.trim();
            const content = contentElement.textContent.trim();

            // 确认是关闭会话的弹窗
            if (title.includes('确认关闭当前会话')) {
                aiLogger.info("[自动关闭] 检测到关闭会话确认弹窗");

                // 检查弹窗类型
                if (content.includes('您还未回复消费者')) {
                    // 第二种：未回复过的弹窗 - 点击"暂不关闭"
                    aiLogger.info("[自动关闭] 检测到未回复弹窗，点击暂不关闭");
                    const cancelBtn = modal.querySelector('button.auxo-btn-primary span');
                    if (cancelBtn && cancelBtn.textContent.includes('暂不关闭')) {
                        cancelBtn.parentElement.click();
                        aiLogger.info("[自动关闭] 已点击暂不关闭按钮");
                        resolve('cancel'); // 返回取消状态
                        return;
                    }
                } else {
                    // 第一种：已回复过的弹窗 - 点击"仍要关闭"
                    aiLogger.info("[自动关闭] 检测到已回复弹窗，点击仍要关闭");
                    const confirmBtn = modal.querySelector('button.auxo-btn:not(.auxo-btn-primary) span');
                    if (confirmBtn && confirmBtn.textContent.includes('仍要关闭')) {
                        confirmBtn.parentElement.click();
                        aiLogger.info("[自动关闭] 已点击仍要关闭按钮");
                        resolve('confirm'); // 返回确认状态
                        return;
                    }
                }
            }

            resolve(null);
        });
    }

    // 等待会话关闭完成
    function waitForSessionClose(maxWaitTime = 3000) {
        return new Promise((resolve) => {
            const startTime = Date.now();
            const checkInterval = setInterval(() => {
                // 检查是否还有确认弹窗
                const modal = document.querySelector('.auxo-modal.auxo-modal-confirm');
                if (!modal || modal.offsetParent === null) {
                    clearInterval(checkInterval);
                    aiLogger.info("[自动关闭] 会话关闭完成");
                    resolve(true);
                    return;
                }

                // 超时处理
                if (Date.now() - startTime > maxWaitTime) {
                    clearInterval(checkInterval);
                    aiLogger.warn("[自动关闭] 等待会话关闭超时");
                    resolve(false);
                }
            }, 100);
        });
    }

    function autoCloseCurrentSession() {
        if (!aiConfig.autoCloseSession) return Promise.resolve();

        return new Promise(async (resolve) => {
            try {
                // 设置会话关闭状态锁，防止处理其他用户
                isClosingSession = true;
                aiLogger.info("[自动关闭] 开始尝试关闭当前会话，设置状态锁定");

                // 方法1: 尝试点击关闭按钮
                const closeButtons = [
                    'button[aria-label*="关闭"]',
                    'button[title*="关闭"]',
                    '.close-btn',
                    '.dialog-close',
                    '[data-qa-id*="close"]',
                    'button:contains("关闭")',
                    '.ant-modal-close',
                    '.modal-close'
                ];

                let closed = false;
                for (const selector of closeButtons) {
                    const closeBtn = document.querySelector(selector);
                    if (closeBtn && closeBtn.offsetParent !== null) { // 确保元素可见
                        closeBtn.click();
                        aiLogger.info("[自动关闭] 通过关闭按钮关闭会话: " + selector);
                        closed = true;
                        break;
                    }
                }

                // 方法2: 如果没有找到关闭按钮，尝试Esc键
                if (!closed) {
                    const escEvent = new KeyboardEvent('keydown', {
                        key: 'Escape',
                        code: 'Escape',
                        keyCode: 27,
                        which: 27,
                        bubbles: true,
                        cancelable: true
                    });

                    // 在多个目标上触发Esc事件
                    const targets = [
                        document,
                        document.body,
                        document.activeElement,
                        document.querySelector('.chat-container'),
                        document.querySelector('.message-container'),
                        document.querySelector('[data-qa-id*="chat"]'),
                        document.querySelector('.ant-modal'),
                        document.querySelector('.modal')
                    ].filter(Boolean);

                    targets.forEach(target => {
                        if (target) {
                            target.dispatchEvent(escEvent);
                        }
                    });

                    aiLogger.info("[自动关闭] 已发送Esc键事件到 " + targets.length + " 个目标");
                }

                // 等待一下让关闭操作生效
                await new Promise(resolve => setTimeout(resolve, 500));

                // 检查并处理确认弹窗
                const modalResult = await handleCloseSessionModal();

                if (modalResult === 'cancel') {
                    // 用户未回复，暂不关闭 - 需要重新回复，但保持状态锁定
                    aiLogger.info("[自动关闭] 检测到未回复状态，需要重新回复用户，保持状态锁定");
                    resolve('need_reply');
                    return;
                } else if (modalResult === 'confirm') {
                    // 已确认关闭，等待关闭完成
                    aiLogger.info("[自动关闭] 确认关闭会话，等待关闭完成");
                    const closeSuccess = await waitForSessionClose();
                    // 关闭完成后解除状态锁定
                    isClosingSession = false;
                    aiLogger.info("[自动关闭] 会话关闭完成，解除状态锁定");
                    resolve(closeSuccess ? 'closed' : 'failed');
                    return;
                } else if (modalResult === null) {
                    // 没有弹窗，说明会话已经正常关闭，立即解除状态锁定
                    isClosingSession = false;
                    aiLogger.info("[自动关闭] 未检测到弹窗，会话已正常关闭，解除状态锁定");
                    resolve('closed');
                    return;
                }

                // 方法3: 尝试点击遮罩层或背景区域
                setTimeout(async () => {
                    const overlays = [
                        '.ant-modal-mask',
                        '.modal-mask',
                        '.overlay',
                        '.backdrop'
                    ];

                    for (const selector of overlays) {
                        const overlay = document.querySelector(selector);
                        if (overlay && overlay.offsetParent !== null) {
                            overlay.click();
                            aiLogger.info("[自动关闭] 通过点击遮罩层关闭会话: " + selector);
                            break;
                        }
                    }

                    // 再次检查弹窗
                    await new Promise(resolve => setTimeout(resolve, 300));
                    const finalModalResult = await handleCloseSessionModal();

                    if (finalModalResult === 'cancel') {
                        // 保持状态锁定，需要重新回复
                        aiLogger.info("[自动关闭] 最终检测到未回复状态，保持状态锁定");
                        resolve('need_reply');
                    } else if (finalModalResult === 'confirm') {
                        const closeSuccess = await waitForSessionClose();
                        // 关闭完成后解除状态锁定
                        isClosingSession = false;
                        aiLogger.info("[自动关闭] 最终确认关闭完成，解除状态锁定");
                        resolve(closeSuccess ? 'closed' : 'failed');
                    } else {
                        // 没有弹窗，假设已关闭，解除状态锁定
                        isClosingSession = false;
                        aiLogger.info("[自动关闭] 方法3未检测到弹窗，假设已关闭，解除状态锁定");
                        resolve('closed');
                    }
                }, 200);

            } catch (error) {
                // 异常情况下解除状态锁定
                isClosingSession = false;
                aiLogger.error("[自动关闭] 关闭会话失败，解除状态锁定：" + error.message);
                resolve('failed');
            }
        });
    }

    /************************************************************
     * 10. 修改 sendMessageToUser，让其检测并点击特殊触发按钮（增强版 - 支持重新回复）
     ************************************************************/
    function sendMessageToUser(message, doneCallback, username = null) {
        const textarea = document.querySelector('textarea[data-qa-id="qa-send-message-textarea"]');
        const sendBtn = document.querySelector('div[data-qa-id="qa-send-message-button"]');

        if (!textarea || !sendBtn) {
            doneCallback();
            return;
        }

        // 处理自动关闭会话的回调
        const handleAutoClose = async () => {
            if (aiConfig.autoCloseSession) {
                try {
                    const closeResult = await autoCloseCurrentSession();
                    aiLogger.info(`[自动关闭] 关闭结果: ${closeResult}`);

                    if (closeResult === 'need_reply') {
                        // 需要重新回复该用户
                        aiLogger.info("[自动关闭] 检测到需要重新回复，延迟2秒后重新回复");
                        setTimeout(() => {
                            // 重新触发该用户的回复逻辑
                            if (username) {
                                aiLogger.info(`[自动关闭] 开始重新回复用户: ${username}`);
                                // 这里需要重新获取用户消息并回复
                                retryReplyToUser(username);
                            }
                        }, 2000);
                    }
                } catch (error) {
                    aiLogger.error("[自动关闭] 处理自动关闭失败：" + error.message);
                }
            }
        };

        if (typeof message === 'string') {
            // 纯文本
            const rawText = message;
            // 若不想让触发词发给用户，则去掉
            const finalText = aiConfig.sendTriggerWordsToUser ? rawText : removeSpecialTriggerWords(rawText);

            setTextareaValue(textarea, finalText);
            setTimeout(() => {
                sendBtn.click();
                setTimeout(async () => {
                    checkAndClickSpecialSend(rawText); // 检查触发词后点按钮
                    doneCallback();
                    // 回复完成后自动关闭会话
                    await handleAutoClose();
                }, 500);
            }, 500);

        } else if (typeof message === 'object') {
            // 包含 text / images / videos
            const { text, images, videos } = message;
            const rawText = text || "";
            const finalText = aiConfig.sendTriggerWordsToUser ? rawText : removeSpecialTriggerWords(rawText);

            if (finalText) {
                setTextareaValue(textarea, finalText);
                setTimeout(() => {
                    sendBtn.click();
                    setTimeout(() => {
                        sendFiles(images, videos, async () => {
                            checkAndClickSpecialSend(rawText);
                            doneCallback();
                            // 回复完成后自动关闭会话
                            await handleAutoClose();
                        });
                    }, 500);
                }, 500);
            } else {
                // 没有文本，仅发图或视频
                sendFiles(images, videos, async () => {
                    checkAndClickSpecialSend(rawText);
                    doneCallback();
                    // 回复完成后自动关闭会话
                    await handleAutoClose();
                });
            }
        }
    }

    // 重新回复用户的函数（增强版 - 支持状态锁定管理）
    function retryReplyToUser(username) {
        try {
            aiLogger.info(`[重新回复] 开始重新回复用户: ${username}，当前状态锁定: ${isClosingSession}`);

            // 查找该用户的聊天项
            const chatItems = document.querySelectorAll('.list_items .list_item');
            let targetChatItem = null;

            for (const item of chatItems) {
                const nameElement = item.querySelector('[data-qa-id="qa-chat-list-item-name"]');
                if (nameElement && nameElement.textContent.trim() === username) {
                    targetChatItem = item;
                    break;
                }
            }

            if (!targetChatItem) {
                aiLogger.warn(`[重新回复] 未找到用户 ${username} 的聊天项`);
                return;
            }

            // 点击该用户的聊天项
            targetChatItem.click();
            aiLogger.info(`[重新回复] 已点击用户 ${username} 的聊天项`);

            // 延迟一下等待聊天界面加载，然后重新获取消息并回复
            setTimeout(() => {
                // 重新获取该用户的最新消息
                const messageElements = document.querySelectorAll('.message-item, .chat-message, [data-qa-id*="message"]');
                if (messageElements.length > 0) {
                    const lastMessage = messageElements[messageElements.length - 1];
                    const messageText = lastMessage.textContent.trim();

                    if (messageText) {
                        aiLogger.info(`[重新回复] 获取到用户消息: ${messageText}`);

                        // 重新进行回复逻辑
                        const category = getMatchedCategoryForOrder(null);
                        let matchedConfig = null;

                        // 检查关键词匹配
                        for (const kw of category.keywords) {
                            if (kw.keyword && messageText.includes(kw.keyword)) {
                                matchedConfig = kw;
                                break;
                            }
                        }

                        // 如果没有关键词匹配，使用默认回复
                        if (!matchedConfig) {
                            const fallbackMsg = getFallbackReply(username);
                            if (fallbackMsg) {
                                matchedConfig = {
                                    replyType: 'text',
                                    text: fallbackMsg
                                };
                            }
                        }

                        if (matchedConfig) {
                            const replyMsg = getAutoReplyMessage(messageText, matchedConfig, username);
                            if (replyMsg) {
                                aiLogger.info(`[重新回复] 准备发送回复: ${typeof replyMsg === 'string' ? replyMsg : JSON.stringify(replyMsg)}`);
                                sendMessageToUser(replyMsg, () => {
                                    aiLogger.info(`[重新回复] 重新回复完成: ${username}`);
                                    // 更新回复记录
                                    repliedMessagesMap[username] = {
                                        text: messageText,
                                        time: Date.now()
                                    };

                                    // 重新回复完成后，再次尝试关闭会话
                                    setTimeout(async () => {
                                        try {
                                            const closeResult = await autoCloseCurrentSession();
                                            aiLogger.info(`[重新回复] 重新关闭会话结果: ${closeResult}`);

                                            // 如果仍然需要重新回复，则递归处理（防止无限循环，最多重试3次）
                                            if (closeResult === 'need_reply') {
                                                const retryCount = (window.retryCloseCount || 0) + 1;
                                                if (retryCount <= 3) {
                                                    window.retryCloseCount = retryCount;
                                                    aiLogger.warn(`[重新回复] 仍需重新回复，第${retryCount}次重试`);
                                                    setTimeout(() => retryReplyToUser(username), 2000);
                                                } else {
                                                    // 超过重试次数，强制解除状态锁定
                                                    isClosingSession = false;
                                                    window.retryCloseCount = 0;
                                                    aiLogger.error(`[重新回复] 重试次数超限，强制解除状态锁定`);
                                                }
                                            } else {
                                                // 成功关闭或失败，重置重试计数
                                                window.retryCloseCount = 0;
                                            }
                                        } catch (error) {
                                            // 异常情况下解除状态锁定
                                            isClosingSession = false;
                                            window.retryCloseCount = 0;
                                            aiLogger.error(`[重新回复] 重新关闭会话异常，解除状态锁定: ${error.message}`);
                                        }
                                    }, 1000); // 延迟1秒后尝试关闭
                                }, username);
                            }
                        }
                    }
                }
            }, 1000);

        } catch (error) {
            // 异常情况下解除状态锁定
            isClosingSession = false;
            aiLogger.error(`[重新回复] 重新回复用户 ${username} 失败，解除状态锁定: ${error.message}`);
        }
    }

    function setTextareaValue(textarea, val) {
        const nativeInputValueSetter = Object.getOwnPropertyDescriptor(window.HTMLTextAreaElement.prototype, 'value').set;
        nativeInputValueSetter.call(textarea, val);
        const inputEvent = new Event('input', { bubbles: true });
        textarea.dispatchEvent(inputEvent);
    }

    // 去除触发词
    function removeSpecialTriggerWords(msg) {
        let res = msg;
        specialSendTriggers.forEach(item => {
            res = res.replaceAll(item.triggerText, "");
        });
        return res;
    }

    // 检查自动回复中是否包含特殊词
    function checkAndClickSpecialSend(rawReply) {
        for (const item of specialSendTriggers) {
            if (rawReply.includes(item.triggerText)) {
                clickOrderSendButtonByIndex(item.btnIndex);
            }
        }
    }

    // 点击订单区域第n个"发送"按钮
    function clickOrderSendButtonByIndex(btnIndex) {
        const allSendSpans = document.querySelectorAll(
            '#mona-workbench_订单 button.ecom-btn.ecom-btn-link.ecom-btn-sm.orderOptions span'
        );
        if (allSendSpans.length > btnIndex) {
            allSendSpans[btnIndex].click();
            aiLogger.log("[AI监控] 已点击第", btnIndex + 1, "个订单发送按钮 =>", allSendSpans[btnIndex].textContent);
        } else {
            aiLogger.warn("[AI监控] 未找到下标为", btnIndex, "的订单发送按钮");
        }
    }

    // 发送图片或视频
    const sendFiles = async (images, videos, doneCallback) => {
        try {
            // 合并所有媒体文件到一个队列
            let mediaQueue = [];

            // 从数据库加载图片
        if (images && images.length > 0) {
                const loadedImages = await loadMediaBatch(images);
                loadedImages.forEach((imgData, index) => {
                    if (imgData) {
                        mediaQueue.push({
                            base64: imgData,
                            type: 'image/png'
                        });
                    } else {
                        console.warn(`[AI监控] 无法加载图片: ${images[index]}`);
                    }
                });
            }

            // 从数据库加载视频
        if (videos && videos.length > 0) {
                const loadedVideos = await loadMediaBatch(videos);
                loadedVideos.forEach((vidData, index) => {
                    if (vidData) {
                        mediaQueue.push({
                            base64: vidData,
                            type: 'video/mp4'
                        });
                    } else {
                        console.warn(`[AI监控] 无法加载视频: ${videos[index]}`);
                    }
                });
            }

            // 没有媒体文件需要发送
            if (mediaQueue.length === 0) {
            doneCallback();
                return;
            }

            console.log("[AI监控] 准备发送媒体队列，共", mediaQueue.length, "个文件");

            // 按顺序处理媒体文件
            let currentIndex = 0;

            // 定义处理单个媒体文件的函数
            const processNextMedia = () => {
                if (currentIndex >= mediaQueue.length) {
                    console.log("[AI监控] 所有媒体文件处理完成");
                    doneCallback();
                    return;
                }

                const currentItem = mediaQueue[currentIndex];
                console.log("[AI监控] 开始处理第", currentIndex + 1, "个媒体文件");

                // 清理上一个可能存在的观察器
                if (window.currentMediaObserver) {
                    window.currentMediaObserver.disconnect();
                    window.currentMediaObserver = null;
                }

                // 创建新的观察器
                const observer = new MutationObserver((mutations) => {
                    const fileSendBtn = document.querySelector('button[data-qa-id="qa-send-file-popup-submit"]');

                    if (fileSendBtn) {
                        console.log("[AI监控] 找到文件发送按钮，准备点击");

                        // 断开观察器
                        observer.disconnect();
                        window.currentMediaObserver = null;

                        // 点击发送按钮
                        fileSendBtn.click();
                        console.log("[AI监控] 已点击发送按钮");

                        // 处理下一个媒体文件
                        currentIndex++;
                        setTimeout(processNextMedia, 1500);
                    }
                });

                // 保存当前观察器的引用
                window.currentMediaObserver = observer;

                // 开始观察DOM变化
                observer.observe(document.body, {
                    childList: true,
                    subtree: true
                });

                // 注入当前媒体文件
        const textarea = document.querySelector('textarea[data-qa-id="qa-send-message-textarea"]');
                if (!textarea) {
                    console.log("[AI监控] 未找到文本输入区域，跳过当前媒体");
                    currentIndex++;
                    setTimeout(processNextMedia, 500);
                    return;
                }

                try {
                    // 解析并注入文件
                    console.log("[AI监控] 拖拽媒体文件到输入框");
                    injectMediaToTextarea(currentItem.base64, currentItem.type, textarea);
                } catch (error) {
                    console.error("[AI监控] 媒体文件注入失败:", error);
                    currentIndex++;
                    setTimeout(processNextMedia, 500);
                }

                // 设置超时保护
                setTimeout(() => {
                    if (window.currentMediaObserver === observer) {
                        console.log("[AI监控] 处理当前媒体超时，继续下一个");
                        observer.disconnect();
                        window.currentMediaObserver = null;
                        currentIndex++;
                        processNextMedia();
                    }
                }, 10000);
            };

            // 开始处理第一个媒体文件
            processNextMedia();
        } catch (error) {
            console.error("[AI监控] 发送媒体文件出错:", error);
            doneCallback(); // 确保即使出错也能继续流程
        }
    };

    // 注入媒体文件到文本区域
    function injectMediaToTextarea(base64Data, mimeType, textarea) {
        try {
            // 检查输入
            if (!base64Data || !base64Data.includes('base64')) {
                console.error("[AI监控] 无效的base64数据");
                return false;
            }

            // 转换base64为文件对象
        const byteString = atob(base64Data.split(',')[1]);
        const ab = new ArrayBuffer(byteString.length);
        const ia = new Uint8Array(ab);
        for (let i = 0; i < byteString.length; i++) {
            ia[i] = byteString.charCodeAt(i);
        }

        const blob = new Blob([ab], { type: mimeType });
        const file = new File([blob], mimeType.startsWith('image/') ? 'image.png' : 'video.mp4', { type: mimeType });

            // 创建DataTransfer对象
        const dataTransfer = new DataTransfer();
        dataTransfer.items.add(file);

            // 模拟拖放事件
        const dragEnter = new DragEvent('dragenter', { bubbles: true, cancelable: true, dataTransfer });
        textarea.dispatchEvent(dragEnter);

        const dragOver = new DragEvent('dragover', { bubbles: true, cancelable: true, dataTransfer });
        textarea.dispatchEvent(dragOver);

        const drop = new DragEvent('drop', { bubbles: true, cancelable: true, dataTransfer });
        textarea.dispatchEvent(drop);

            return true;
        } catch (error) {
            console.error("[AI监控] 注入媒体文件时出错:", error);
            return false;
        }
    }

    /************************************************************
     * 10. AI 接口请求逻辑
     ************************************************************/
    function getAIResponse(userMessage, chatMessages, orderInfo) {
        return new Promise((resolve, reject) => {
            if (!aiConfig.apiURL || !aiConfig.applicationID || !aiConfig.appAuthorization) {
                return reject(new Error("AI配置不完整"));
            }

            let orderText = '';
            if (orderInfo && !orderInfo.noOrders && orderInfo.orders && orderInfo.orders.length > 0) {
                orderText = orderInfo.orders.map(o => {
                    return `订单${o.orderNumber}：商品名="${o.name}" 状态="${o.status}"`;
                }).join('\n');
            } else {
                orderText = '暂无相关订单信息';
            }

            const prompt =
`以下是用户的订单信息：
${orderText}

用户最新的问题是："${userMessage}"

你是抖店客服AI聊天助手，是一个抖店客服智能体，负责为用户提供服务，解答关于商品信息、订单状态、修改地址等问题，因为所有的订单都是用户在抖音超级福袋活动中奖所得，都是0元支付免费赠送给用户的，是不支持取消、退货、退款、换货的，所以绝不能提及退款、退货、换货话术，用户兑奖后订单就无法取消的，并且用户兑奖后订单就会显示出来，所以奖品会在48小时内给用户安排发出，并且也不支持折现。`;

            const openUrl = aiConfig.apiURL + "/application/" + aiConfig.applicationID + "/chat/open";

            // 先获取 chat_id
            GM_xmlhttpRequest({
                method: "GET",
                url: openUrl,
                headers: {
                    "AUTHORIZATION": aiConfig.appAuthorization,
                    "accept": "application/json"
                },
                onload: (openRes) => {
                    if (openRes.status === 200) {
                        let openData = JSON.parse(openRes.responseText);
                        if (openData.code === 200 && openData.data) {
                            const chatId = openData.data;
                            const chatUrl = aiConfig.apiURL + "/application/chat_message/" + chatId;

                            // 再向 chat_message 发消息
                            GM_xmlhttpRequest({
                                method: "POST",
                                url: chatUrl,
                                headers: {
                                    "AUTHORIZATION": aiConfig.appAuthorization,
                                    "accept": "application/json",
                                    "Content-Type": "application/json"
                                },
                                data: JSON.stringify({
                                    message: prompt,
                                    re_chat: false,
                                    stream: false
                                }),
                                onload: (chatRes) => {
                                    if (chatRes.status === 200) {
                                        let chatData = JSON.parse(chatRes.responseText);
                                        if (chatData.code === 200 && chatData.data && chatData.data.content) {
                                            resolve(chatData.data.content);
                                        } else {
                                            reject(new Error("AI回复格式错误或无内容"));
                                        }
                                    } else {
                                        reject(new Error("AI接口请求失败，状态码：" + chatRes.status));
                                    }
                                },
                                onerror: (err) => {
                                    reject(new Error("AI接口请求错误：" + JSON.stringify(err)));
                                }
                            });
                        } else {
                            reject(new Error("获取chat_id失败：" + openRes.responseText));
                        }
                    } else {
                        reject(new Error("获取chat_id接口请求失败，状态码：" + openRes.status));
                    }
                },
                onerror: (err) => {
                    reject(new Error("获取chat_id接口请求错误：" + JSON.stringify(err)));
                }
            });
        });
    }

    /************************************************************
     * 11. 启动轮询、监控
     ************************************************************/
    console.clear();
    aiLogger.log("%c[AI监控] 抖店飞鸽AI自动聊天监控 - 上下文AI+特殊触发词版", "font-weight:bold;color:black;");
    aiLogger.log("%c[AI监控] 脚本已启动，等待页面加载...", "color:black;");
    showStatusMessage("脚本已启动，等待页面加载...", "blue");

    pollForChatList();
    monitorOrderInfo();
    startMonitoringRecovery(); // 启动监控恢复机制
    detectManualReplyCompletion(); // 启动手动回复检测

    /************************************************************
     * 9. 插入"AI配置"按钮
     ************************************************************/
    const configToggleBtn = document.createElement('button');
    configToggleBtn.textContent = 'AI配置';
    Object.assign(configToggleBtn.style, {
        position: 'fixed',
        bottom: '10px',
        left: '50%',
        transform: 'translateX(-50%)',
        zIndex: 9999,
        background: '#1966ff',
        color: '#fff',
        border: 'none',
        borderRadius: '5px',
        padding: '5px 10px',
        cursor: 'pointer'
    });
    configToggleBtn.addEventListener('click', () => {
        configContainer.style.display = (configContainer.style.display === 'none' ? 'block' : 'none');
    });
    document.body.appendChild(configToggleBtn);

})();
