// ==UserScript==
// @name         抖店飞鸽AI自动聊天监控（上下文AI回复+分类组高级版）+特殊触发按钮
// @namespace    http://tampermonkey.net/
// @version      1.65
// @description  实时监控抖店飞鸽聊天，自动回复。支持分类组管理、关键词回复、多媒体、默认回复、AI接口配置、配置导出导入。无关键词时将用户+客服聊天记录与订单信息传给AI模型获取上下文智能回复。并且可以在关键词回复内容里添加特殊触发词([订单商品规格]、[核对收货信息]、[承诺发货时间]等)来点击相应"发送"按钮。
// @match        https://im.jinritemai.com/pc_seller_v2/main/workspace
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_xmlhttpRequest
// @grant        GM_deleteValue
// @grant        unsafeWindow
// ==/UserScript==

(function () {
    'use strict';

    /****************************************************************
     * 1. 新增：定义特殊触发词数组（文字 -> 点击哪个"发送"按钮）
     *    根据业务需求，可自行增减、调整触发词和按钮下标
     ****************************************************************/
    const specialSendTriggers = [
        { triggerText: "[订单商品规格]", btnIndex: 0 },
        { triggerText: "[核对收货信息]", btnIndex: 1 },
        { triggerText: "[承诺发货时间]", btnIndex: 2 }
    ];

    /************************************************************
     * 原有变量、默认分类组等
     ************************************************************/
    let isNoUser = false;
    let lastStatusMap = {};
    let lastSelectedUser = null;
    let lastOrderInfo = null;
    let lastChatMessages = [];
    let isOrderMonitoring = false;
    let repliedMessagesMap = {};

    // 默认分类组示例
    const defaultCategories = [
        { name: "通用", keyword: "", order: 0, isDefault: true, keywords: [] },
        { name: "五代耳机", keyword: "耳机", order: 1, keywords: [] },
        { name: "头戴式耳机", keyword: "头戴", order: 2, keywords: [] },
        { name: "吨吨杯", keyword: "杯", order: 3, keywords: [] },
        { name: "口红", keyword: "口红", order: 4, keywords: [] },
        { name: "手表", keyword: "手表", order: 5, keywords: [] },
        { name: "电饭煲", keyword: "电饭煲", order: 6, keywords: [] },
        { name: "银吊坠", keyword: "吊坠", order: 7, keywords: [] },
        { name: "扫地机", keyword: "扫地机", order: 8, keywords: [] },
        { name: "平板", keyword: "平板", order: 9, keywords: [] }
    ];

    /************************************************************
     * 媒体文件存储系统 - 使用IndexedDB代替GM_setValue存储大型媒体文件
     ************************************************************/
    const DB_NAME = 'FigeonMediaStore';
    const DB_VERSION = 2; // 更新版本号从1到2
    const MEDIA_STORE = 'mediaFiles';

    // 初始化数据库
    const initMediaDB = () => {
        return new Promise((resolve, reject) => {
            // 首先尝试获取当前数据库版本
            const checkRequest = indexedDB.open(DB_NAME);

            checkRequest.onsuccess = (event) => {
                const currentVersion = event.target.result.version;
                event.target.result.close();

                // 使用当前版本或DB_VERSION中较大的值打开数据库
                const useVersion = Math.max(currentVersion, DB_VERSION);
                const request = indexedDB.open(DB_NAME, useVersion);

                request.onerror = (event) => {
                    console.error('[AI监控] 打开IndexedDB失败:', event.target.error);
                    reject(event.target.error);
                };

                request.onupgradeneeded = (event) => {
                    const db = event.target.result;
                    // 创建媒体文件存储对象仓库
                    if (!db.objectStoreNames.contains(MEDIA_STORE)) {
                        db.createObjectStore(MEDIA_STORE, { keyPath: 'id' });
                        console.log('[AI监控] 媒体存储对象仓库创建成功');
                    }
                };

                request.onsuccess = (event) => {
                    const db = event.target.result;
                    console.log(`[AI监控] IndexedDB媒体存储连接成功 (版本: ${db.version})`);
                    resolve(db);
                };
            };

            checkRequest.onerror = (event) => {
                console.error('[AI监控] 检查IndexedDB版本失败:', event.target.error);

                // 如果检查失败，尝试直接用指定版本打开
                const request = indexedDB.open(DB_NAME, DB_VERSION);

                request.onerror = (event) => {
                    console.error('[AI监控] 打开IndexedDB失败:', event.target.error);
                    reject(event.target.error);
                };

                request.onupgradeneeded = (event) => {
                    const db = event.target.result;
                    if (!db.objectStoreNames.contains(MEDIA_STORE)) {
                        db.createObjectStore(MEDIA_STORE, { keyPath: 'id' });
                        console.log('[AI监控] 媒体存储对象仓库创建成功');
                    }
                };

                request.onsuccess = (event) => {
                    const db = event.target.result;
                    console.log(`[AI监控] IndexedDB媒体存储连接成功 (版本: ${db.version})`);
                    resolve(db);
                };
            };
        });
    };

    // 保存媒体文件到数据库，返回媒体ID
    const saveMediaToDB = (mediaData, mediaType) => {
        return new Promise((resolve, reject) => {
            // 生成唯一ID
            const mediaId = 'media_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

            // 尝试存储到IndexedDB
            initMediaDB().then(db => {
                const transaction = db.transaction([MEDIA_STORE], 'readwrite');
                const store = transaction.objectStore(MEDIA_STORE);

                // 存储媒体数据和类型
                const storeRequest = store.put({
                    id: mediaId,
                    data: mediaData,
                    type: mediaType,
                    timestamp: Date.now()
                });

                storeRequest.onsuccess = () => {
                    console.log(`[AI监控] 媒体文件保存成功，ID: ${mediaId}`);
                    resolve(mediaId);
                };

                storeRequest.onerror = (event) => {
                    console.error('[AI监控] 保存媒体文件失败:', event.target.error);
                    fallbackMediaStorage(mediaId, mediaData);
                    resolve(mediaId); // 继续使用ID，后续会从内存中读取
                };

                transaction.oncomplete = () => {
                    db.close();
                };
            }).catch(error => {
                console.error('[AI监控] 初始化媒体数据库失败:', error);
                // 使用备选存储方法
                fallbackMediaStorage(mediaId, mediaData);
                resolve(mediaId); // 继续使用ID，后续会从内存中读取
            });
        });
    };

    // 内存中的媒体文件备选存储
    const mediaMemoryStore = {};

    // 当IndexedDB不可用时，使用内存存储作为备选
    const fallbackMediaStorage = (mediaId, mediaData) => {
        mediaMemoryStore[mediaId] = mediaData;
        console.log(`[AI监控] 媒体文件已存储在内存中，ID: ${mediaId}`);
        // 显示警告信息
        showStatusMessage('媒体文件暂存在内存中。请注意，刷新页面后这些文件将丢失。', 'orange');
    };

    // 从数据库加载媒体文件
    const loadMediaFromDB = (mediaId) => {
        return new Promise((resolve, reject) => {
            if (!mediaId || !mediaId.startsWith('media_')) {
                // 如果是旧的完整base64数据，直接返回
                resolve(mediaId);
                return;
            }

            // 首先尝试从内存存储中读取
            if (mediaMemoryStore[mediaId]) {
                console.log(`[AI监控] 从内存中加载媒体文件，ID: ${mediaId}`);
                resolve(mediaMemoryStore[mediaId]);
                return;
            }

            // 从IndexedDB加载
            initMediaDB().then(db => {
                const transaction = db.transaction([MEDIA_STORE], 'readonly');
                const store = transaction.objectStore(MEDIA_STORE);
                const getRequest = store.get(mediaId);

                getRequest.onsuccess = (event) => {
                    const result = event.target.result;
                    if (result) {
                        console.log(`[AI监控] 加载媒体文件成功，ID: ${mediaId}`);
                        // 同时缓存到内存中以加快后续访问
                        mediaMemoryStore[mediaId] = result.data;
                        resolve(result.data);
                    } else {
                        console.warn(`[AI监控] 未找到ID为${mediaId}的媒体文件`);
                        resolve(null);
                    }
                };

                getRequest.onerror = (event) => {
                    console.error('[AI监控] 加载媒体文件失败:', event.target.error);
                    resolve(null); // 返回null而不是拒绝Promise
                };

                transaction.oncomplete = () => {
                    db.close();
                };
            }).catch(error => {
                console.error('[AI监控] 初始化媒体数据库失败:', error);
                resolve(null); // 返回null而不是拒绝Promise
            });
        });
    };

    // 批量加载媒体文件
    const loadMediaBatch = async (mediaIds) => {
        const results = [];
        for (const mediaId of mediaIds) {
            try {
                const data = await loadMediaFromDB(mediaId);
                results.push(data);
            } catch (error) {
                console.error(`[AI监控] 加载媒体ID ${mediaId} 失败:`, error);
                results.push(null);
            }
        }
        return results;
    };

    // 删除媒体文件
    const deleteMediaFromDB = (mediaId) => {
        return new Promise((resolve, reject) => {
            if (!mediaId || !mediaId.startsWith('media_')) {
                resolve(true); // 旧数据无需删除
                return;
            }

            // 删除内存中的缓存
            if (mediaMemoryStore[mediaId]) {
                delete mediaMemoryStore[mediaId];
                console.log(`[AI监控] 已从内存中删除媒体文件，ID: ${mediaId}`);
            }

            // 从IndexedDB中删除
            initMediaDB().then(db => {
                const transaction = db.transaction([MEDIA_STORE], 'readwrite');
                const store = transaction.objectStore(MEDIA_STORE);
                const deleteRequest = store.delete(mediaId);

                deleteRequest.onsuccess = () => {
                    console.log(`[AI监控] 删除媒体文件成功，ID: ${mediaId}`);
                    resolve(true);
                };

                deleteRequest.onerror = (event) => {
                    console.error('[AI监控] 删除媒体文件失败:', event.target.error);
                    // 即使IndexedDB删除失败，已从内存中删除，仍然视为成功
                    resolve(true);
                };

                transaction.oncomplete = () => {
                    db.close();
                };
            }).catch(error => {
                console.error('[AI监控] 初始化媒体数据库失败:', error);
                // 至少从内存中删除了，视为部分成功
                resolve(true);
            });
        });
    };

    // 初始化媒体数据库
    initMediaDB().catch(error => {
        console.error('[AI监控] 初始化媒体数据库失败:', error);
        handleDBInitFailure(error);
    });

    // 处理数据库初始化失败的情况
    const handleDBInitFailure = (error) => {
        // 添加一个提示，指导用户刷新页面或清除缓存
        const errorMessage = error.toString();
        if (errorMessage.includes('version')) {
            const messageDiv = document.createElement('div');
            messageDiv.style.cssText = 'position:fixed;top:10px;left:10px;background:red;color:white;padding:10px;z-index:9999;border-radius:5px;max-width:80%;';
            messageDiv.innerHTML = `
                <p><strong>[AI监控]</strong> IndexedDB版本不匹配，请尝试以下操作：</p>
                <ol>
                    <li>刷新页面（按F5键）</li>
                    <li>如仍有问题，请尝试清除浏览器缓存</li>
                    <li>或者，重新安装脚本最新版本</li>
                </ol>
                <button id="closeDbErrorMsg" style="background:white;color:red;border:none;padding:5px 10px;border-radius:3px;cursor:pointer;margin-top:5px;">关闭提示</button>
            `;
            document.body.appendChild(messageDiv);

            document.getElementById('closeDbErrorMsg').addEventListener('click', () => {
                messageDiv.remove();
            });
        }
    };

    /************************************************************
     * 在 aiConfig 里新增一个布尔开关：sendTriggerWordsToUser，
     * 用于控制"是否把触发词也一起发送给用户"（默认 false 不发送）
     ************************************************************/
    let aiConfig = {
        categories: defaultCategories,
        fallbackReply: "您好，请问有什么可以帮助您的？",
        apiURL: "",
        applicationID: "",
        appAuthorization: "",
        useAIFallback: false,
        sendTriggerWordsToUser: false // 默认不发送触发词给用户
    };

    const statusesToAutoReply = [
        "message_group_name_waitReply",
        "message_group_name_autoReply",
        "message_group_name_overThreemins"
    ];

    /************************************************************
     * 配置分块存储与读取函数
     ************************************************************/
    const CONFIG_CHUNK_SIZE = 100000; // 每块大小限制约100KB
    const CONFIG_MAIN_KEY = "figeonAiConfig_main";

    // 保存配置到存储
    function saveConfigToStorage() {
        try {
            // 清理旧的分块存储
            clearStoredConfig();

            // 序列化配置
            const configStr = JSON.stringify(aiConfig);

            // 如果配置较小，直接存储
            if (configStr.length < CONFIG_CHUNK_SIZE) {
                GM_setValue(CONFIG_MAIN_KEY, configStr);
                return;
            }

            // 分块存储
            const chunks = Math.ceil(configStr.length / CONFIG_CHUNK_SIZE);

            // 存储主信息
            const mainInfo = {
                chunks: chunks,
                totalSize: configStr.length,
                lastSaved: new Date().getTime()
            };
            GM_setValue(CONFIG_MAIN_KEY, JSON.stringify(mainInfo));

            // 分块存储主体
            for (let i = 0; i < chunks; i++) {
                const start = i * CONFIG_CHUNK_SIZE;
                const end = Math.min(start + CONFIG_CHUNK_SIZE, configStr.length);
                const chunk = configStr.substring(start, end);
                GM_setValue(`figeonAiConfig_chunk_${i}`, chunk);
            }

            console.log(`[AI监控] 配置已分${chunks}块保存，总大小: ${(configStr.length/1024).toFixed(2)}KB`);
        } catch (error) {
            console.error("[AI监控] 保存配置失败:", error);
        }
    }

    // 从存储加载配置
    function loadConfigFromStorage() {
        try {
            // 尝试获取主信息
            const mainData = GM_getValue(CONFIG_MAIN_KEY);
            if (!mainData) {
                console.log("[AI监控] 未找到配置数据");
                return false;
            }

            // 检查是否是直接存储的配置
            try {
                const parsed = JSON.parse(mainData);
                if (parsed && typeof parsed === 'object') {
                    // 检查是否是主信息对象
                    if (parsed.chunks && parsed.totalSize) {
                        // 这是分块存储信息，需要合并块
                        let configStr = "";
                        for (let i = 0; i < parsed.chunks; i++) {
                            const chunkKey = `figeonAiConfig_chunk_${i}`;
                            const chunk = GM_getValue(chunkKey);
                            if (!chunk) {
                                console.error(`[AI监控] 配置块 ${i} 丢失`);
                                return false;
                            }
                            configStr += chunk;
                        }

                        // 检查大小是否匹配
                        if (configStr.length !== parsed.totalSize) {
                            console.warn(`[AI监控] 配置大小不匹配: 预期${parsed.totalSize}字节, 实际${configStr.length}字节`);
                        }

                        processLoadedConfig(configStr);
                    } else {
                        // 这是直接存储的配置
                        processLoadedConfig(mainData);
                    }
                    return true;
                }
            } catch (e) {
                console.error("[AI监控] 解析配置失败:", e);
                return false;
            }
        } catch (error) {
            console.error("[AI监控] 加载配置失败:", error);
            return false;
        }
    }

    // 处理加载的配置数据
    function processLoadedConfig(configStr) {
        try {
            const parsed = JSON.parse(configStr);
            if (parsed && typeof parsed === 'object') {
                aiConfig = parsed;
                // 如果 categories 不存在，就用默认
                if (!aiConfig.categories) {
                    const oldKeywords = aiConfig.keywords || [];
                    aiConfig.categories = JSON.parse(JSON.stringify(defaultCategories));
                    const commonCategory = aiConfig.categories.find(c => c.name === "通用");
                    if (commonCategory) {
                        commonCategory.keywords = oldKeywords;
                    }
                    delete aiConfig.keywords;
                }
                aiConfig.categories.forEach(cat => {
                    if (cat.order === undefined) cat.order = 999;
                    if (!cat.keywords) cat.keywords = [];
                });
                // 根据 order 排序分类组
                aiConfig.categories.sort((a, b) => a.order - b.order);

                // 如果没有 sendTriggerWordsToUser 字段，就补上
                if (typeof aiConfig.sendTriggerWordsToUser === 'undefined') {
                    aiConfig.sendTriggerWordsToUser = false;
                }

                console.log("[AI监控] 配置加载成功");
            }
        } catch (e) {
            console.error("[AI监控] 处理配置数据失败:", e);
        }
    }

    // 清理所有存储的配置块
    function clearStoredConfig() {
        try {
            const mainData = GM_getValue(CONFIG_MAIN_KEY);
            if (mainData) {
                try {
                    const parsed = JSON.parse(mainData);
                    if (parsed && parsed.chunks) {
                        for (let i = 0; i < parsed.chunks; i++) {
                            GM_deleteValue(`figeonAiConfig_chunk_${i}`);
                        }
                    }
                } catch (e) {
                    // 忽略解析错误
                }
            }

            // 兼容旧版本存储
            GM_deleteValue("figeonAiConfig");
        } catch (error) {
            console.error("[AI监控] 清理配置失败:", error);
        }
    }

    // 加载配置
    loadConfigFromStorage();

    /************************************************************
     * 读取配置（从 GM_value 中）
     ************************************************************/
    // 保留旧的加载逻辑作为备份，以防新方法失败
    (function loadConfigFromStorageOld() {
        const storedConfig = GM_getValue("figeonAiConfig");
        if (storedConfig && !loadConfigFromStorage()) {
            try {
                const parsed = JSON.parse(storedConfig);
                if (parsed && typeof parsed === 'object') {
                    aiConfig = parsed;
                    // 如果 categories 不存在，就用默认
                    if (!aiConfig.categories) {
                        const oldKeywords = aiConfig.keywords || [];
                        aiConfig.categories = JSON.parse(JSON.stringify(defaultCategories));
                        const commonCategory = aiConfig.categories.find(c => c.name === "通用");
                        if (commonCategory) {
                            commonCategory.keywords = oldKeywords;
                        }
                        delete aiConfig.keywords;
                    }
                    aiConfig.categories.forEach(cat => {
                        if (cat.order === undefined) cat.order = 999;
                        if (!cat.keywords) cat.keywords = [];
                    });
                    // 根据 order 排序分类组
                    aiConfig.categories.sort((a, b) => a.order - b.order);

                    // 如果没有 sendTriggerWordsToUser 字段，就补上
                    if (typeof aiConfig.sendTriggerWordsToUser === 'undefined') {
                        aiConfig.sendTriggerWordsToUser = false;
                    }
                }
            } catch (e) {
                aiConfig = {
                    categories: defaultCategories,
                    fallbackReply: "您好，请问有什么可以帮助您的？",
                    apiURL: "",
                    applicationID: "",
                    appAuthorization: "",
                    useAIFallback: false,
                    sendTriggerWordsToUser: false
                };
            }
        }
    })();

    /************************************************************
     * 日志工具
     ************************************************************/
    const aiLogger = {
        log: (...args) => console.log(...args),
        warn: (...args) => console.warn(...args),
        error: (...args) => console.error(...args),
        info: (...args) => console.info(...args)
    };

    /************************************************************
     * 顶部提示条（3秒自动消失）
     ************************************************************/
    const statusDiv = document.createElement("div");
    statusDiv.id = "ai-monitor-status";
    Object.assign(statusDiv.style, {
        position: "fixed",
        top: "10px",
        right: "10px",
        zIndex: "9999",
        backgroundColor: "green",
        color: "white",
        padding: "10px",
        borderRadius: "5px",
        fontSize: "14px",
        boxShadow: "0 0 10px rgba(0,0,0,0.5)",
        display: "none"
    });
    document.body.appendChild(statusDiv);

    let lastMessage = "";
    const showStatusMessage = (message, color = "green") => {
        if (lastMessage === message) return;
        lastMessage = message;
        statusDiv.style.backgroundColor = color;
        statusDiv.innerText = message;
        statusDiv.style.display = "block";

        setTimeout(() => {
            statusDiv.style.display = "none";
            lastMessage = "";
        }, 3000);
    };

    /************************************************************
     * 一些辅助函数
     ************************************************************/
    const getStatusText = (statusKey) => {
        const statusMap = {
            "message_group_name_waitReply": "待回复",
            "message_group_name_humanReply": "人工已回复",
            "message_group_name_autoReply": "机器人已回复",
            "message_group_name_overThreemins": "超时待回复"
        };
        return statusMap[statusKey] || "未知状态";
    };

    const debounce = (func, delay) => {
        let timeout;
        return (...args) => {
            clearTimeout(timeout);
            timeout = setTimeout(() => func(...args), delay);
        };
    };

    const getUsernameAndMessage = (userItem) => {
        const usernameElement = userItem.querySelector('[class*="ACbDgAKLLGYz9MPeTYzw"] span');
        const username = usernameElement ? usernameElement.textContent.trim() : "未知用户";
        const messageElement = userItem.querySelector('[class*="qn15NQQrTVBNHreW_xHW"]');
        let message = "无消息";
        if (messageElement) {
            const spans = Array.from(messageElement.querySelectorAll("span"));
            message = spans.map(span => span.textContent.trim()).filter(text => text).join(" ");
        }
        return { username, message };
    };

    /************************************************************
     * 控制台输出更新
     ************************************************************/
    const updateConsoleOutput = () => {
        console.clear();
        aiLogger.log("%c[AI监控] 抖店飞鸽AI自动聊天监控", "font-weight:bold;color:black;");

        if (isNoUser) {
            aiLogger.log("%c[AI监控] 暂无会话中用户", "color:red;font-weight:bold;");
        }

        for (const [statusKey, { statusText, users }] of Object.entries(lastStatusMap)) {
            aiLogger.log(`%c[AI监控] 切换到状态：${statusText}`, "color:red;font-weight:bold;");
            for (const [username, message] of Object.entries(users)) {
                aiLogger.log(`%c[AI监控] ${statusText}-【${username}】：${message}`, "color:green;font-weight:bold;");
                if (lastOrderInfo && lastOrderInfo.username === username) {
                    if (lastOrderInfo.noOrders) {
                        aiLogger.log(`%c[AI监控] 【${lastOrderInfo.username}】：暂无180天内订单`, "color:blue;font-weight:bold;");
                    } else if (lastOrderInfo.orders) {
                        lastOrderInfo.orders.forEach((order) => {
                            aiLogger.log(`%c[AI监控] ${order.status}-【${order.username}】：${order.name}（订单：${order.orderNumber}）`, "color:blue;font-weight:bold;");
                        });
                    }
                }
            }
        }

        const isUserInRealtime = Object.values(lastStatusMap).some(({ users }) => lastOrderInfo && lastOrderInfo.username in users);
        if (lastOrderInfo && !isUserInRealtime) {
            aiLogger.log("%c[AI监控] 选中用户订单信息：", "color:gray;font-weight:bold;");
            if (lastOrderInfo.noOrders) {
                aiLogger.log(`%c[AI监控] 【${lastOrderInfo.username}】：暂无180天内订单`, "color:gray;font-weight:bold;");
            } else if (lastOrderInfo.orders) {
                lastOrderInfo.orders.forEach((order) => {
                    aiLogger.log(`%c[AI监控] ${order.status}-【${order.username}】：${order.name}（订单：${order.orderNumber}）`, "color:gray;font-weight:bold;");
                });
            }
        }

        if (lastChatMessages && lastChatMessages.length > 0) {
            aiLogger.log(`%c[AI监控] 【${lastSelectedUser}】聊天记录：`, "color:blue;font-weight:bold;");
            lastChatMessages.forEach((msg) => {
                let messageColor = msg.sender === '用户' ? 'gray' : 'black';
                let prefix = `【${msg.sender} ${msg.time || ''}】：`;
                if (msg.type === '文本') {
                    aiLogger.log(`%c[AI监控] ${prefix}${msg.content}`, `color:${messageColor};font-weight:bold;`);
                } else if (msg.type === '图片') {
                    aiLogger.log(`%c[AI监控] ${prefix}[图片] - ${msg.content}`, `color:${messageColor};font-weight:bold;`);
                } else if (msg.type === '视频') {
                    aiLogger.log(`%c[AI监控] ${prefix}[视频] - ${msg.content}`, `color:${messageColor};font-weight:bold;`);
                } else {
                    aiLogger.log(`%c[AI监控] ${prefix}[未知类型]`, `color:${messageColor};font-weight:bold;`);
                }
            });
        }
    };

    /************************************************************
     * 处理聊天列表
     ************************************************************/
    const processChatList = (chatListContainer) => {
        try {
            const noUserElement = chatListContainer.querySelector("[class*='IKQkMhdDbeSPU1WM3187']");
            if (noUserElement) {
                // 无会话
                if (!isNoUser) {
                    isNoUser = true;
                    lastStatusMap = {};
                    showStatusMessage("暂无会话中用户", "red");
                    updateConsoleOutput();
                }
            } else {
                // 有会话
                if (isNoUser) {
                    isNoUser = false;
                    showStatusMessage("有用户会话", "green");
                }

                const chatItems = chatListContainer.querySelectorAll("[class*='ReactVirtualized__Grid__innerScrollContainer'] > div");
                if (chatItems.length > 0) {
                    let currentStatusKey = null;
                    let statusMap = {};

                    chatItems.forEach(item => {
                        const statusSpan = item.querySelector('span[data-btm^="message_group_name_"]');
                        if (statusSpan) {
                            currentStatusKey = statusSpan.getAttribute("data-btm");
                            const statusText = getStatusText(currentStatusKey);
                            if (!statusMap[currentStatusKey]) {
                                statusMap[currentStatusKey] = { statusText, users: {} };
                            }
                        } else if (item.querySelector('[data-btm-id]')) {
                            const userItem = item.querySelector('[data-btm-id]');
                            if (userItem && currentStatusKey) {
                                const { username, message } = getUsernameAndMessage(userItem);
                                statusMap[currentStatusKey].users[username] = message;
                            }
                        }
                    });

                    if (JSON.stringify(statusMap) !== JSON.stringify(lastStatusMap)) {
                        lastStatusMap = statusMap;
                        showStatusMessage("聊天状态已更新", "green");
                        updateConsoleOutput();
                    } else {
                        updateConsoleOutput();
                    }
                }
            }

            // 获取当前选中用户
            const selectedUserElement = document.querySelector('div[data-qa-id="qa-user-portrait-username"]');
            if (selectedUserElement) {
                const username = selectedUserElement.textContent.trim();
                if (username && username !== lastSelectedUser) {
                    // 切换选中用户
                    lastSelectedUser = username;
                    lastOrderInfo = null;
                    lastChatMessages = [];
                    observeOrderInfo(username);
                    observeChatMessages();
                } else {
                    if (username && !lastOrderInfo) {
                        observeOrderInfo(username);
                    }
                    if (username && (!lastChatMessages || lastChatMessages.length === 0)) {
                        observeChatMessages();
                    }
                }
            } else {
                // 无选中用户
                waitForSelectedUser();
            }

            autoReplyToWaitReplyUsers();

        } catch (error) {
            aiLogger.error(`[AI监控] 处理聊天列表时出错：${error.message}`);
        }
    };

    const waitForSelectedUser = () => {
        const selectedUserCheckInterval = setInterval(() => {
            const selectedUserElement = document.querySelector('div[data-qa-id="qa-user-portrait-username"]');
            if (selectedUserElement) {
                clearInterval(selectedUserCheckInterval);
                const username = selectedUserElement.textContent.trim();
                lastSelectedUser = username;
                lastOrderInfo = null;
                lastChatMessages = [];
                observeOrderInfo(username);
                observeChatMessages();
            }
        }, 500);
    };

    /************************************************************
     * 监控订单信息
     ************************************************************/
    const observeOrderInfo = (username) => {
        const orderContainer = document.querySelector("#mona-workbench_订单");
        if (orderContainer) {
            if (observeOrderInfo.orderObserver) {
                observeOrderInfo.orderObserver.disconnect();
            }

            showStatusMessage("订单信息加载中...", "blue");
            const orderObserver = new MutationObserver(() => {
                if (lastSelectedUser) {
                    const orderLoaded = fetchOrderInfo(lastSelectedUser);
                    if (orderLoaded) {
                        orderObserver.disconnect();
                        showStatusMessage("订单信息已更新", "green");
                    }
                }
            });

            orderObserver.observe(orderContainer, {
                childList: true,
                subtree: true,
                attributes: true,
                characterData: true
            });

            observeOrderInfo.orderObserver = orderObserver;

            const initialOrderLoaded = fetchOrderInfo(username);
            if (initialOrderLoaded) {
                orderObserver.disconnect();
                showStatusMessage("订单信息已更新", "green");
            }
        } else {
            waitForOrderContainer(username);
        }
    };

    const waitForOrderContainer = (username) => {
        const orderContainerCheckInterval = setInterval(() => {
            const orderContainer = document.querySelector("#mona-workbench_订单");
            if (orderContainer) {
                clearInterval(orderContainerCheckInterval);
                observeOrderInfo(username);
            }
        }, 500);
    };

    const fetchOrderInfo = (username) => {
        try {
            const orderContainer = document.querySelector('#mona-workbench_订单');
            if (!orderContainer) return false;

            const noOrderElement = orderContainer.querySelector('div.ecom-empty-description');
            if (noOrderElement && noOrderElement.textContent.trim() === '暂无180天内订单') {
                const orderInfo = { username, orders: [], noOrders: true };
                if (JSON.stringify(orderInfo) !== JSON.stringify(lastOrderInfo)) {
                    lastOrderInfo = orderInfo;
                    updateConsoleOutput();
                    showStatusMessage("暂无180天内订单", "orange");
                }
                return true;
            }

            const orders = orderContainer.querySelectorAll('div.ecom-collapse-item');
            if (orders.length === 0) return false;

            const orderInfoList = [];
            orders.forEach((order, index) => {
                const orderStatusElement = order.querySelector('div.ecom-sp-tag .sp-tag-content');
                const orderStatus = orderStatusElement ? orderStatusElement.textContent.trim() : '未知状态';

                const orderNameElement = order.querySelector('.textSpecial > div > span');
                const orderName = orderNameElement ? orderNameElement.textContent.trim() : '未知商品';

                if (orderStatus && orderName) {
                    orderInfoList.push({
                        status: orderStatus,
                        name: orderName,
                        username: username,
                        orderNumber: index + 1
                    });
                }
            });

            if (orderInfoList.length > 0) {
                const orderInfo = { username, orders: orderInfoList, noOrders: false };
                if (JSON.stringify(orderInfo) !== JSON.stringify(lastOrderInfo)) {
                    lastOrderInfo = orderInfo;
                    updateConsoleOutput();
                    showStatusMessage("订单信息已更新", "orange");
                }
                return true;
            } else {
                return false;
            }
        } catch (error) {
            aiLogger.error(`[AI监控] 获取订单信息时出错：${error.message}`);
            return false;
        }
    };

    /************************************************************
     * 监控聊天消息
     ************************************************************/
    const observeChatMessages = () => {
        const messageListContainer = document.querySelector('.messageList');
        if (!messageListContainer) {
            waitForMessageListContainer();
            return;
        }

        if (observeChatMessages.chatObserver) {
            observeChatMessages.chatObserver.disconnect();
        }

        const chatObserver = new MutationObserver(() => {
            const messages = fetchChatMessages();
            if (messages) {
                lastChatMessages = messages;
                updateConsoleOutput();
            }
        });

        chatObserver.observe(messageListContainer, {
            childList: true,
            subtree: true,
            characterData: true,
            attributes: true
        });

        observeChatMessages.chatObserver = chatObserver;

        const initialMessages = fetchChatMessages();
        if (initialMessages) {
            lastChatMessages = initialMessages;
            updateConsoleOutput();
        }
    };

    const waitForMessageListContainer = () => {
        const messageListCheckInterval = setInterval(() => {
            const messageListContainer = document.querySelector('.messageList');
            if (messageListContainer) {
                clearInterval(messageListCheckInterval);
                observeChatMessages();
            }
        }, 500);
    };

    const fetchChatMessages = () => {
        try {
            const messageListContainer = document.querySelector('.messageList');
            if (!messageListContainer) return [];
            const messageElements = messageListContainer.querySelectorAll('div[data-qa-id="qa-message-warpper"]');
            if (messageElements.length === 0) return [];

            const messages = [];
            messageElements.forEach(messageElement => {
                let messageType = '未知';
                let messageContent = '';
                let sender = '未知';

                const messageBubble = messageElement.querySelector('.Ie29C7uLyEjZzd8JeS8A');
                if (messageBubble) {
                    if (messageBubble.style.flexDirection === 'row') {
                        sender = '用户';
                    } else if (messageBubble.style.flexDirection === 'row-reverse') {
                        sender = '客服';
                    }
                }

                const messageContentElement = messageElement.querySelector('pre > span');
                if (messageContentElement) {
                    messageType = '文本';
                    messageContent = messageContentElement.textContent.trim();
                } else {
                    const imageElement = messageElement.querySelector('img.WOUA1PDG10lEGvmqa_W3, img.wLLihoJSQT_lF8636mkj');
                    if (imageElement) {
                        const overlayElement = messageElement.querySelector('img.rnXlFPvVe29ACsLydCWg');
                        if (overlayElement) {
                            messageType = '视频';
                            messageContent = imageElement.src;
                        } else {
                            messageType = '图片';
                            messageContent = imageElement.src;
                        }
                    }
                }

                let messageTime = '';
                const messageTimeElement = messageElement.querySelector('.O4UWWFoQxgMq4AWHMq25');
                if (messageTimeElement) {
                    messageTime = messageTimeElement.textContent.trim();
                }

                if (messageType !== '未知') {
                    messages.push({ sender, type: messageType, content: messageContent, time: messageTime });
                }
            });

            return messages;
        } catch (error) {
            aiLogger.error(`[AI监控] 获取聊天消息时出错：${error.message}`);
            return [];
        }
    };

    /************************************************************
     * 开始监听
     ************************************************************/
    const debounceProcessChatList = debounce(processChatList, 100);

    const startMonitoring = (chatListContainer) => {
        aiLogger.log("%c[AI监控] 聊天列表容器已加载，开始实时监测...", "color:black;");
        showStatusMessage("聊天列表容器已加载，开始监测！", "green");

        const observer = new MutationObserver(() => {
            debounceProcessChatList(chatListContainer);
        });

        observer.observe(chatListContainer, {
            childList: true,
            subtree: true,
            attributes: true,
            characterData: true
        });

        processChatList(chatListContainer);
    };

    const pollForChatList = () => {
        aiLogger.log("%c[AI监控] 启动容器轮询...", "color:black;");
        const pollInterval = setInterval(() => {
            const chatArea = document.querySelector("#chantListScrollArea");
            if (!chatArea) {
                return;
            }

            const chatListContainer = chatArea.querySelector("[class*='ReactVirtualized__Grid__innerScrollContainer']");
            if (!chatListContainer) {
                if (!isNoUser) {
                    isNoUser = true;
                    lastStatusMap = {};
                    showStatusMessage("暂无会话中用户", "red");
                    updateConsoleOutput();
                }
                return;
            }

            aiLogger.log("%c[AI监控] 聊天列表容器已找到！", "color:black;");
            showStatusMessage("聊天列表容器已找到！", "blue");
            clearInterval(pollInterval);

            startMonitoring(chatListContainer);
            observeChatArea(chatArea);
        }, 1000);
    };

    const observeChatArea = (chatArea) => {
        const chatAreaObserver = new MutationObserver(() => {
            const chatListContainer = chatArea.querySelector("[class*='ReactVirtualized__Grid__innerScrollContainer']");
            if (chatListContainer && isNoUser) {
                aiLogger.log("%c[AI监控] 聊天列表容器重新加载！", "color:black;");
                showStatusMessage("聊天列表容器重新加载！", "blue");
                isNoUser = false;
                startMonitoring(chatListContainer);
            } else if (!chatListContainer && !isNoUser) {
                isNoUser = true;
                lastStatusMap = {};
                showStatusMessage("暂无会话中用户", "red");
                updateConsoleOutput();
            }
        });

        chatAreaObserver.observe(chatArea, {
            childList: true,
            subtree: true
        });
    };

    const monitorOrderInfo = () => {
        if (isOrderMonitoring) return;
        isOrderMonitoring = true;

        const monitor = () => {
            const selectedUserElement = document.querySelector('div[data-qa-id="qa-user-portrait-username"]');
            if (selectedUserElement) {
                const username = selectedUserElement.textContent.trim();
                if (username && username !== lastSelectedUser) {
                    lastSelectedUser = username;
                    lastOrderInfo = null;
                    lastChatMessages = [];
                    observeOrderInfo(username);
                    observeChatMessages();
                } else {
                    if (username && !lastOrderInfo) {
                        observeOrderInfo(username);
                    }
                    if (username && (!lastChatMessages || lastChatMessages.length === 0)) {
                        observeChatMessages();
                    }
                }
            } else {
                waitForSelectedUser();
            }
        };

        const interval = setInterval(monitor, 1000);
        window.addEventListener('unload', () => clearInterval(interval));
    };

    /************************************************************
     * 以下是配置面板相关 DOM、UI 构建与逻辑
     * （分类组管理、关键词管理、导出导入、AI配置等）
     ************************************************************/
    let currentCategoryIndex = 0;
    let keywordListCollapsed = false; // 折叠状态

    // 这里包含了分类组UI、关键词列表UI、默认回复、AI接口配置、特殊按钮触发词展示等等
    // ------------------------- 配置面板容器 -------------------------
    const configContainer = document.createElement('div');
    configContainer.id = 'figeonConfigContainer';
    Object.assign(configContainer.style, {
        position: 'fixed',
        bottom: '50px',
        left: '50%',
        transform: 'translateX(-50%)',
        zIndex: 9999,
        background: '#fff',
        borderRadius: '10px',
        boxShadow: '0 0 10px rgba(0,0,0,0.3)',
        padding: '20px',
        minWidth: '600px',
        maxWidth: '800px',
        fontFamily: 'Arial, sans-serif',
        display: 'none',
        color: '#333'
    });

    /************************************************************
     * 1. 标题 + 简介
     ************************************************************/
    const title = document.createElement('h3');
    title.textContent = 'AI自动回复配置';
    title.style.marginTop = '0';
    configContainer.appendChild(title);

    const desc = document.createElement('p');
    desc.textContent = '通过分类组及关键词配置实现智能回复。右键分类组可编辑，点击分类组右上角X可删除，拖动分类组按钮进行排序。';
    desc.style.fontSize = '14px';
    desc.style.color = '#666';
    configContainer.appendChild(desc);

    /************************************************************
     * 2. 分类组导航区
     ************************************************************/
    const categoryNavContainer = document.createElement('div');
    Object.assign(categoryNavContainer.style, {
        display: 'flex',
        flexWrap: 'wrap',
        gap: '5px',
        marginBottom: '10px',
        maxWidth: '100%'
    });
    configContainer.appendChild(categoryNavContainer);

    let dragStartIndex = null;

    function renderCategoryNav() {
        categoryNavContainer.innerHTML = '';
        aiConfig.categories.sort((a, b) => a.order - b.order);

        aiConfig.categories.forEach((cat, index) => {
            const catWrapper = document.createElement('div');
            catWrapper.style.position = 'relative';
            catWrapper.style.display = 'inline-flex';
            catWrapper.style.alignItems = 'center';
            catWrapper.style.border = '1px solid #ddd';
            catWrapper.style.borderRadius = '5px';
            catWrapper.style.padding = '0';
            catWrapper.style.backgroundColor = index === currentCategoryIndex ? '#1966ff' : '#f0f0f0';
            catWrapper.draggable = true;
            catWrapper.dataset.index = index;

            catWrapper.addEventListener('dragstart', (e) => {
                dragStartIndex = parseInt(catWrapper.dataset.index, 10);
                e.dataTransfer.effectAllowed = 'move';
                e.dataTransfer.setData('text/plain', '');
                catWrapper.style.opacity = '0.5';
            });

            catWrapper.addEventListener('dragend', () => {
                catWrapper.style.opacity = '1';
            });

            catWrapper.addEventListener('dragover', (e) => {
                e.preventDefault();
            });

            catWrapper.addEventListener('drop', (e) => {
                e.preventDefault();
                const dropIndex = parseInt(catWrapper.dataset.index, 10);
                if (dragStartIndex !== null && dragStartIndex !== dropIndex) {
                    const movedCat = aiConfig.categories.splice(dragStartIndex, 1)[0];
                    aiConfig.categories.splice(dropIndex, 0, movedCat);
                    aiConfig.categories.forEach((c, i) => c.order = i);
                    dragStartIndex = null;
                    renderCategoryNav();
                    renderKeywords();
                }
            });

            const catBtn = document.createElement('button');
            catBtn.textContent = cat.name;
            Object.assign(catBtn.style, {
                backgroundColor: 'transparent',
                color: index === currentCategoryIndex ? '#fff' : '#333',
                border: 'none',
                borderRadius: '0',
                padding: '5px 10px',
                cursor: 'pointer',
                userSelect: 'none',
                maxWidth: '100px',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
            });

            catBtn.addEventListener('click', () => {
                currentCategoryIndex = index;
                renderCategoryNav();
                renderKeywords();
            });

            catBtn.addEventListener('contextmenu', (e) => {
                e.preventDefault();
                showEditCategoryOverlay(index);
            });

            catWrapper.appendChild(catBtn);

            if (!cat.isDefault) {
                const delBtn = document.createElement('div');
                delBtn.textContent = '×';
                Object.assign(delBtn.style, {
                    position: 'absolute',
                    top: '2px',
                    right: '2px',
                    width: '15px',
                    height: '15px',
                    background: 'red',
                    color: '#fff',
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    cursor: 'pointer',
                    fontWeight: 'bold',
                    fontSize: '12px'
                });
                delBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    if (confirm(`确认删除分类组"${cat.name}"吗？`)) {
                        aiConfig.categories.splice(index, 1);
                        if (currentCategoryIndex >= aiConfig.categories.length) {
                            currentCategoryIndex = aiConfig.categories.length - 1;
                        }
                        renderCategoryNav();
                        renderKeywords();
                    }
                });
                catWrapper.appendChild(delBtn);
            }

            categoryNavContainer.appendChild(catWrapper);
        });

        // 新增分类组按钮
        const addCategoryBtn = document.createElement('button');
        addCategoryBtn.textContent = '+新增分类组';
        Object.assign(addCategoryBtn.style, {
            backgroundColor: '#28a745',
            color: '#fff',
            border: 'none',
            borderRadius: '5px',
            padding: '5px 10px',
            cursor: 'pointer'
        });
        addCategoryBtn.addEventListener('click', () => {
            showAddCategoryOverlay();
        });
        categoryNavContainer.appendChild(addCategoryBtn);
    }

    // 编辑分类组弹窗
    let editCategoryOverlay = null;
    function showEditCategoryOverlay(index) {
        const category = aiConfig.categories[index];
        if (!editCategoryOverlay) {
            editCategoryOverlay = document.createElement('div');
            Object.assign(editCategoryOverlay.style, {
                position: 'fixed',
                top: '0',
                left: '0',
                width: '100%',
                height: '100%',
                backgroundColor: 'rgba(0,0,0,0.5)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                zIndex: 10000
            });

            const overlayContent = document.createElement('div');
            Object.assign(overlayContent.style, {
                background: '#fff',
                padding: '20px',
                borderRadius: '10px',
                boxShadow: '0 0 10px rgba(0,0,0,0.3)',
                minWidth: '300px'
            });

            const overlayTitle = document.createElement('h3');
            overlayTitle.textContent = '编辑分类组';
            overlayContent.appendChild(overlayTitle);

            const nameInput = document.createElement('input');
            nameInput.type = 'text';
            nameInput.placeholder = '分类组名称';
            Object.assign(nameInput.style, {
                width: '100%',
                marginBottom: '10px',
                boxSizing: 'border-box',
                padding: '5px'
            });
            overlayContent.appendChild(nameInput);

            const keywordInput = document.createElement('input');
            keywordInput.type = 'text';
            keywordInput.placeholder = '分类组关键词';
            Object.assign(keywordInput.style, {
                width: '100%',
                marginBottom: '10px',
                boxSizing: 'border-box',
                padding: '5px'
            });
            overlayContent.appendChild(keywordInput);

            const orderInput = document.createElement('input');
            orderInput.type = 'number';
            orderInput.placeholder = '排序值(数字越小越前)';
            Object.assign(orderInput.style, {
                width: '100%',
                marginBottom: '10px',
                boxSizing: 'border-box',
                padding: '5px'
            });
            overlayContent.appendChild(orderInput);

            const btnContainer = document.createElement('div');
            btnContainer.style.textAlign = 'right';

            const confirmBtn = document.createElement('button');
            confirmBtn.textContent = '确认';
            Object.assign(confirmBtn.style, {
                backgroundColor: '#1966ff',
                color: '#fff',
                border: 'none',
                borderRadius: '5px',
                padding: '5px 10px',
                marginRight: '10px',
                cursor: 'pointer'
            });
            confirmBtn.addEventListener('click', () => {
                const nameVal = nameInput.value.trim();
                const keywordVal = keywordInput.value.trim();
                const orderVal = parseInt(orderInput.value.trim() || "999", 10);
                if (!nameVal) {
                    alert('请输入分类组名称');
                    return;
                }
                category.name = nameVal;
                category.keyword = keywordVal;
                category.order = orderVal;
                aiConfig.categories.sort((a, b) => a.order - b.order);
                renderCategoryNav();
                renderKeywords();
                editCategoryOverlay.style.display = 'none';
            });

            const cancelBtn = document.createElement('button');
            cancelBtn.textContent = '取消';
            Object.assign(cancelBtn.style, {
                backgroundColor: '#aaa',
                color: '#fff',
                border: 'none',
                borderRadius: '5px',
                padding: '5px 10px',
                cursor: 'pointer'
            });
            cancelBtn.addEventListener('click', () => {
                editCategoryOverlay.style.display = 'none';
            });

            btnContainer.appendChild(confirmBtn);
            btnContainer.appendChild(cancelBtn);
            overlayContent.appendChild(btnContainer);

            editCategoryOverlay.appendChild(overlayContent);
            document.body.appendChild(editCategoryOverlay);

            editCategoryOverlay._nameInput = nameInput;
            editCategoryOverlay._keywordInput = keywordInput;
            editCategoryOverlay._orderInput = orderInput;
        }

        editCategoryOverlay._nameInput.value = category.name;
        editCategoryOverlay._keywordInput.value = category.keyword;
        editCategoryOverlay._orderInput.value = category.order;
        editCategoryOverlay.style.display = 'flex';
    }

    // 新增分类组弹窗
    let addCategoryOverlay = null;
    function showAddCategoryOverlay() {
        if (!addCategoryOverlay) {
            addCategoryOverlay = document.createElement('div');
            Object.assign(addCategoryOverlay.style, {
                position: 'fixed',
                top: '0',
                left: '0',
                width: '100%',
                height: '100%',
                backgroundColor: 'rgba(0,0,0,0.5)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                zIndex: 10000
            });

            const overlayContent = document.createElement('div');
            Object.assign(overlayContent.style, {
                background: '#fff',
                padding: '20px',
                borderRadius: '10px',
                boxShadow: '0 0 10px rgba(0,0,0,0.3)',
                minWidth: '300px'
            });

            const overlayTitle = document.createElement('h3');
            overlayTitle.textContent = '新增分类组';
            overlayContent.appendChild(overlayTitle);

            const nameInput = document.createElement('input');
            nameInput.type = 'text';
            nameInput.placeholder = '分类组名称';
            Object.assign(nameInput.style, {
                width: '100%',
                marginBottom: '10px',
                boxSizing: 'border-box',
                padding: '5px'
            });
            overlayContent.appendChild(nameInput);

            const keywordInput = document.createElement('input');
            keywordInput.type = 'text';
            keywordInput.placeholder = '分类组关键词';
            Object.assign(keywordInput.style, {
                width: '100%',
                marginBottom: '10px',
                boxSizing: 'border-box',
                padding: '5px'
            });
            overlayContent.appendChild(keywordInput);

            const maxOrder = aiConfig.categories.length > 0 ? Math.max(...aiConfig.categories.map(c => c.order)) : 0;

            const orderInput = document.createElement('input');
            orderInput.type = 'number';
            orderInput.placeholder = '排序值(数字越小越前)';
            orderInput.value = maxOrder + 1;
            Object.assign(orderInput.style, {
                width: '100%',
                marginBottom: '10px',
                boxSizing: 'border-box',
                padding: '5px'
            });
            overlayContent.appendChild(orderInput);

            const btnContainer = document.createElement('div');
            btnContainer.style.textAlign = 'right';

            const confirmBtn = document.createElement('button');
            confirmBtn.textContent = '确认';
            Object.assign(confirmBtn.style, {
                backgroundColor: '#1966ff',
                color: '#fff',
                border: 'none',
                borderRadius: '5px',
                padding: '5px 10px',
                marginRight: '10px',
                cursor: 'pointer'
            });
            confirmBtn.addEventListener('click', () => {
                const nameVal = nameInput.value.trim();
                const keywordVal = keywordInput.value.trim();
                const orderVal = parseInt(orderInput.value.trim() || "999", 10);
                if (!nameVal) {
                    alert('请输入分类组名称');
                    return;
                }
                aiConfig.categories.push({
                    name: nameVal,
                    keyword: keywordVal,
                    order: orderVal,
                    keywords: []
                });
                aiConfig.categories.sort((a, b) => a.order - b.order);
                renderCategoryNav();
                renderKeywords();
                addCategoryOverlay.style.display = 'none';
            });

            const cancelBtn = document.createElement('button');
            cancelBtn.textContent = '取消';
            Object.assign(cancelBtn.style, {
                backgroundColor: '#aaa',
                color: '#fff',
                border: 'none',
                borderRadius: '5px',
                padding: '5px 10px',
                cursor: 'pointer'
            });
            cancelBtn.addEventListener('click', () => {
                addCategoryOverlay.style.display = 'none';
            });

            btnContainer.appendChild(confirmBtn);
            btnContainer.appendChild(cancelBtn);
            overlayContent.appendChild(btnContainer);

            addCategoryOverlay.appendChild(overlayContent);
            document.body.appendChild(addCategoryOverlay);

            addCategoryOverlay._nameInput = nameInput;
            addCategoryOverlay._keywordInput = keywordInput;
            addCategoryOverlay._orderInput = orderInput;
        }

        const maxOrder = aiConfig.categories.length > 0 ? Math.max(...aiConfig.categories.map(c => c.order)) : 0;
        addCategoryOverlay._nameInput.value = '';
        addCategoryOverlay._keywordInput.value = '';
        addCategoryOverlay._orderInput.value = maxOrder + 1;
        addCategoryOverlay.style.display = 'flex';
    }

    /************************************************************
     * 3. 关键词回复列表
     ************************************************************/
    const keywordListContainer = document.createElement('div');
    Object.assign(keywordListContainer.style, {
        border: '1px solid #ccc',
        borderRadius: '5px',
        padding: '10px',
        marginBottom: '10px',
        maxHeight: '300px',
        overflowY: 'auto'
    });

    const keywordListHeader = document.createElement('div');
    keywordListHeader.style.display = 'flex';
    keywordListHeader.style.justifyContent = 'space-between';
    keywordListHeader.style.alignItems = 'center';

    const keywordListTitle = document.createElement('h4');
    keywordListTitle.textContent = '关键词回复列表';
    keywordListTitle.style.margin = '0';
    keywordListHeader.appendChild(keywordListTitle);

    const collapseBtn = document.createElement('button');
    collapseBtn.textContent = keywordListCollapsed ? '▼' : '▲';
    Object.assign(collapseBtn.style, {
        backgroundColor: '#ccc',
        border: 'none',
        borderRadius: '5px',
        padding: '5px 10px',
        cursor: 'pointer',
        fontSize: '12px'
    });
    collapseBtn.addEventListener('click', () => {
        keywordListCollapsed = !keywordListCollapsed;
        collapseBtn.textContent = keywordListCollapsed ? '▼' : '▲';
        renderKeywords();
    });
    keywordListHeader.appendChild(collapseBtn);

    keywordListContainer.appendChild(keywordListHeader);

    const addKeywordBtn = document.createElement('button');
    addKeywordBtn.textContent = '添加关键词';
    Object.assign(addKeywordBtn.style, {
        marginBottom: '10px',
        display: 'block',
        backgroundColor: '#1966ff',
        color: '#fff',
        border: 'none',
        borderRadius: '5px',
        padding: '5px 10px',
        cursor: 'pointer'
    });

    const keywordItemsContainer = document.createElement('div');

    function renderKeywords() {
        keywordItemsContainer.innerHTML = '';
        const currentCategory = aiConfig.categories[currentCategoryIndex];

        if (!keywordListCollapsed) {
            currentCategory.keywords.forEach((item, index) => {
                const itemDiv = document.createElement('div');
                itemDiv.style.marginBottom = '10px';
                itemDiv.style.border = '1px solid #eee';
                itemDiv.style.padding = '10px';
                itemDiv.style.borderRadius = '5px';
                itemDiv.style.position = 'relative';

                const removeBtn = document.createElement('button');
                removeBtn.textContent = '删除';
                Object.assign(removeBtn.style, {
                    position: 'absolute',
                    top: '10px',
                    right: '10px',
                    background: 'red',
                    color: '#fff',
                    border: 'none',
                    borderRadius: '5px',
                    cursor: 'pointer'
                });
                removeBtn.addEventListener('click', () => {
                    currentCategory.keywords.splice(index, 1);
                    renderKeywords();
                });
                itemDiv.appendChild(removeBtn);

                const keywordInput = document.createElement('input');
                keywordInput.type = 'text';
                keywordInput.value = item.keyword;
                keywordInput.placeholder = '关键词';
                Object.assign(keywordInput.style, {
                    width: '100%',
                    padding: '5px',
                    marginBottom: '5px',
                    boxSizing: 'border-box'
                });
                keywordInput.addEventListener('input', () => {
                    currentCategory.keywords[index].keyword = keywordInput.value.trim();
                });
                itemDiv.appendChild(keywordInput);

                // 移除replyType选择器，统一使用"文本+文件"模式
                // 为兼容旧配置，保留item.replyType字段但不再显示选择器

                const replyInput = document.createElement('input');
                replyInput.type = 'text';
                replyInput.value = item.text || '';
                replyInput.placeholder = '回复内容（文本，可为空）';
                Object.assign(replyInput.style, {
                    width: '100%',
                    padding: '5px',
                    marginBottom: '5px',
                    boxSizing: 'border-box'
                });
                replyInput.addEventListener('input', () => {
                    currentCategory.keywords[index].text = replyInput.value.trim();
                });
                itemDiv.appendChild(replyInput);

                const mediaBtnContainer = document.createElement('div');
                mediaBtnContainer.style.marginBottom = '5px';

                const imageUploadBtn = document.createElement('button');
                imageUploadBtn.textContent = '上传图片';
                Object.assign(imageUploadBtn.style, {
                    backgroundColor: '#28a745',
                    color: '#fff',
                    border: 'none',
                    borderRadius: '5px',
                    padding: '5px 10px',
                    cursor: 'pointer',
                    marginRight: '5px'
                });
                imageUploadBtn.addEventListener('click', () => {
                    const fileInput = document.createElement('input');
                    fileInput.type = 'file';
                    fileInput.accept = 'image/*';
                    fileInput.multiple = true;
                    fileInput.onchange = () => {
                        const files = Array.from(fileInput.files);
                        const promises = [];

                        files.forEach(file => {
                            const reader = new FileReader();
                            const promise = new Promise((resolve) => {
                                reader.onload = async (e) => {
                                const base64 = e.target.result;
                                    try {
                                        // 保存到IndexedDB中，并获取媒体ID
                                        const mediaId = await saveMediaToDB(base64, 'image');
                                if (!currentCategory.keywords[index].images) {
                                    currentCategory.keywords[index].images = [];
                                }
                                        currentCategory.keywords[index].images.push(mediaId);
                                        resolve();
                                    } catch (error) {
                                        console.error('[AI监控] 保存图片失败:', error);
                                        resolve();
                                    }
                                };
                            });
                            promises.push(promise);
                            reader.readAsDataURL(file);
                        });

                        // 所有图片处理完成后重新渲染
                        Promise.all(promises).then(() => {
                            renderKeywords();
                        });
                    };
                    fileInput.click();
                });
                mediaBtnContainer.appendChild(imageUploadBtn);

                const videoUploadBtn = document.createElement('button');
                videoUploadBtn.textContent = '上传视频';
                Object.assign(videoUploadBtn.style, {
                    backgroundColor: '#17a2b8',
                    color: '#fff',
                    border: 'none',
                    borderRadius: '5px',
                    padding: '5px 10px',
                    cursor: 'pointer'
                });
                videoUploadBtn.addEventListener('click', () => {
                    const fileInput = document.createElement('input');
                    fileInput.type = 'file';
                    fileInput.accept = 'video/*';
                    fileInput.multiple = true;
                    fileInput.onchange = () => {
                        const files = Array.from(fileInput.files);
                        const promises = [];

                        files.forEach(file => {
                            const reader = new FileReader();
                            const promise = new Promise((resolve) => {
                                reader.onload = async (e) => {
                                const base64 = e.target.result;
                                    try {
                                        // 保存到IndexedDB中，并获取媒体ID
                                        const mediaId = await saveMediaToDB(base64, 'video');
                                if (!currentCategory.keywords[index].videos) {
                                    currentCategory.keywords[index].videos = [];
                                }
                                        currentCategory.keywords[index].videos.push(mediaId);
                                        resolve();
                                    } catch (error) {
                                        console.error('[AI监控] 保存视频失败:', error);
                                        resolve();
                                    }
                                };
                            });
                            promises.push(promise);
                            reader.readAsDataURL(file);
                        });

                        // 所有视频处理完成后重新渲染
                        Promise.all(promises).then(() => {
                            renderKeywords();
                        });
                    };
                    fileInput.click();
                });
                mediaBtnContainer.appendChild(videoUploadBtn);

                itemDiv.appendChild(mediaBtnContainer);

                // 显示图片、视频缩略图并支持删除
                if (item.images && item.images.length > 0) {
                    const imagesPreview = document.createElement('div');
                    imagesPreview.style.display = 'flex';
                    imagesPreview.style.flexWrap = 'wrap';
                    imagesPreview.style.marginTop = '5px';

                    // 创建一个加载状态显示
                    const loadingDiv = document.createElement('div');
                    loadingDiv.textContent = '正在加载媒体文件...';
                    loadingDiv.style.padding = '5px';
                    loadingDiv.style.color = '#666';
                    imagesPreview.appendChild(loadingDiv);
                    itemDiv.appendChild(imagesPreview);

                    // 异步加载所有图片
                    (async () => {
                        try {
                            // 批量加载所有图片数据
                            const imageDataList = await loadMediaBatch(item.images);

                            // 清除加载状态
                            imagesPreview.innerHTML = '';

                            // 显示所有图片
                            imageDataList.forEach((imgData, imgIndex) => {
                                if (!imgData) {
                                    console.warn(`[AI监控] 图片 ${item.images[imgIndex]} 加载失败`);
                                    return;
                                }

                        const imgWrapper = document.createElement('div');
                        imgWrapper.style.position = 'relative';
                        imgWrapper.style.marginRight = '5px';
                        imgWrapper.style.marginBottom = '5px';

                        const img = document.createElement('img');
                                img.src = imgData; // 使用加载的数据
                        img.style.width = '50px';
                        img.style.height = '50px';
                        img.style.objectFit = 'cover';
                        img.style.borderRadius = '5px';
                        imgWrapper.appendChild(img);

                        const delImgBtn = document.createElement('div');
                        delImgBtn.textContent = '×';
                        Object.assign(delImgBtn.style, {
                            position: 'absolute',
                            top: '0',
                            right: '0',
                            width: '15px',
                            height: '15px',
                            background: 'red',
                            color: '#fff',
                            borderRadius: '50%',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            cursor: 'pointer',
                            fontWeight: 'bold',
                            fontSize: '12px'
                        });
                                delImgBtn.addEventListener('click', async () => {
                                    // 从数据库中删除媒体文件
                                    const mediaId = item.images[imgIndex];
                                    if (mediaId.startsWith('media_')) {
                                        try {
                                            await deleteMediaFromDB(mediaId);
                                        } catch (error) {
                                            console.error(`[AI监控] 删除媒体文件 ${mediaId} 失败:`, error);
                                        }
                                    }

                                    // 从配置中移除
                            currentCategory.keywords[index].images.splice(imgIndex, 1);
                            renderKeywords();
                        });

                        imgWrapper.appendChild(delImgBtn);
                        imagesPreview.appendChild(imgWrapper);
                    });
                        } catch (error) {
                            console.error('[AI监控] 加载图片缩略图失败:', error);
                            imagesPreview.innerHTML = '<div style="color: red;">加载图片失败</div>';
                        }
                    })();
                }

                if (item.videos && item.videos.length > 0) {
                    const videosPreview = document.createElement('div');
                    videosPreview.style.display = 'flex';
                    videosPreview.style.flexWrap = 'wrap';
                    videosPreview.style.marginTop = '5px';

                    // 创建一个加载状态显示
                    const loadingDiv = document.createElement('div');
                    loadingDiv.textContent = '正在加载媒体文件...';
                    loadingDiv.style.padding = '5px';
                    loadingDiv.style.color = '#666';
                    videosPreview.appendChild(loadingDiv);
                    itemDiv.appendChild(videosPreview);

                    // 异步加载所有视频
                    (async () => {
                        try {
                            // 批量加载所有视频数据
                            const videoDataList = await loadMediaBatch(item.videos);

                            // 清除加载状态
                            videosPreview.innerHTML = '';

                            // 显示所有视频
                            videoDataList.forEach((vidData, vidIndex) => {
                                if (!vidData) {
                                    console.warn(`[AI监控] 视频 ${item.videos[vidIndex]} 加载失败`);
                                    return;
                                }

                        const vidWrapper = document.createElement('div');
                        vidWrapper.style.position = 'relative';
                        vidWrapper.style.marginRight = '5px';
                        vidWrapper.style.marginBottom = '5px';

                        const video = document.createElement('video');
                                video.src = vidData; // 使用加载的数据
                        video.style.width = '50px';
                        video.style.height = '50px';
                        video.style.objectFit = 'cover';
                        video.style.borderRadius = '5px';
                        video.controls = false;
                        vidWrapper.appendChild(video);

                        const delVidBtn = document.createElement('div');
                        delVidBtn.textContent = '×';
                        Object.assign(delVidBtn.style, {
                            position: 'absolute',
                            top: '0',
                            right: '0',
                            width: '15px',
                            height: '15px',
                            background: 'red',
                            color: '#fff',
                            borderRadius: '50%',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            cursor: 'pointer',
                            fontWeight: 'bold',
                            fontSize: '12px'
                        });
                                delVidBtn.addEventListener('click', async () => {
                                    // 从数据库中删除媒体文件
                                    const mediaId = item.videos[vidIndex];
                                    if (mediaId.startsWith('media_')) {
                                        try {
                                            await deleteMediaFromDB(mediaId);
                                        } catch (error) {
                                            console.error(`[AI监控] 删除媒体文件 ${mediaId} 失败:`, error);
                                        }
                                    }

                                    // 从配置中移除
                            currentCategory.keywords[index].videos.splice(vidIndex, 1);
                            renderKeywords();
                        });
                        vidWrapper.appendChild(delVidBtn);
                        videosPreview.appendChild(vidWrapper);
                    });
                        } catch (error) {
                            console.error('[AI监控] 加载视频缩略图失败:', error);
                            videosPreview.innerHTML = '<div style="color: red;">加载视频失败</div>';
                        }
                    })();
                }

                keywordItemsContainer.appendChild(itemDiv);
            });
        }
    }

    addKeywordBtn.addEventListener('click', () => {
        const currentCategory = aiConfig.categories[currentCategoryIndex];
        currentCategory.keywords.push({
            keyword: '',
            replyType: 'textWithFile', // 统一为新的回复类型
            text: '',
            images: [],
            videos: []
        });
        renderKeywords();
    });

    keywordListContainer.appendChild(addKeywordBtn);
    keywordListContainer.appendChild(keywordItemsContainer);
    configContainer.appendChild(keywordListContainer);

    /************************************************************
     * 4. 默认回复配置
     ************************************************************/
    const fallbackContainer = document.createElement('div');
    Object.assign(fallbackContainer.style, {
        border: '1px solid #ccc',
        borderRadius: '5px',
        padding: '10px',
        marginBottom: '10px'
    });

    const fallbackTitle = document.createElement('h4');
    fallbackTitle.textContent = '默认回复';
    fallbackTitle.style.margin = '0 0 10px 0';
    fallbackContainer.appendChild(fallbackTitle);

    const fallbackInput = document.createElement('input');
    fallbackInput.type = 'text';
    fallbackInput.value = aiConfig.fallbackReply;
    fallbackInput.placeholder = '未匹配关键词时的默认回复';
    fallbackInput.style.width = '100%';
    fallbackInput.addEventListener('input', () => {
        aiConfig.fallbackReply = fallbackInput.value.trim();
    });
    fallbackContainer.appendChild(fallbackInput);
    configContainer.appendChild(fallbackContainer);

    /************************************************************
     * 5. AI接口配置区域
     ************************************************************/
    const aiSettingsContainer = document.createElement('div');
    Object.assign(aiSettingsContainer.style, {
        border: '1px solid #ccc',
        borderRadius: '5px',
        padding: '10px',
        marginBottom: '10px'
    });

    const aiSettingsTitle = document.createElement('h4');
    aiSettingsTitle.textContent = 'AI接口配置 (无关键词时使用AI回复)';
    aiSettingsTitle.style.margin = '0 0 10px 0';
    aiSettingsContainer.appendChild(aiSettingsTitle);

    const apiUrlInput = document.createElement('input');
    apiUrlInput.type = 'text';
    apiUrlInput.placeholder = 'API地址，例如：http://xxx.xx.xx.xx:8080/api';
    apiUrlInput.value = aiConfig.apiURL || '';
    Object.assign(apiUrlInput.style, {
        width: '100%',
        padding: '5px',
        marginBottom: '5px',
        boxSizing: 'border-box'
    });
    apiUrlInput.addEventListener('input', () => {
        aiConfig.apiURL = apiUrlInput.value.trim();
    });
    aiSettingsContainer.appendChild(apiUrlInput);

    const appIdInput = document.createElement('input');
    appIdInput.type = 'text';
    appIdInput.placeholder = '应用ID';
    appIdInput.value = aiConfig.applicationID || '';
    Object.assign(appIdInput.style, {
        width: '100%',
        padding: '5px',
        marginBottom: '5px',
        boxSizing: 'border-box'
    });
    appIdInput.addEventListener('input', () => {
        aiConfig.applicationID = appIdInput.value.trim();
    });
    aiSettingsContainer.appendChild(appIdInput);

    const appAuthInput = document.createElement('input');
    appAuthInput.type = 'text';
    appAuthInput.placeholder = '应用密钥(Authorization)';
    appAuthInput.value = aiConfig.appAuthorization || '';
    Object.assign(appAuthInput.style, {
        width: '100%',
        padding: '5px',
        marginBottom: '5px',
        boxSizing: 'border-box'
    });
    appAuthInput.addEventListener('input', () => {
        aiConfig.appAuthorization = appAuthInput.value.trim();
    });
    aiSettingsContainer.appendChild(appAuthInput);

    const useAIFallbackLabel = document.createElement('label');
    useAIFallbackLabel.style.display = 'inline-block';
    useAIFallbackLabel.style.marginRight = '10px';
    useAIFallbackLabel.textContent = '开启AI默认回复模式';
    aiSettingsContainer.appendChild(useAIFallbackLabel);

    const useAIFallbackCheckbox = document.createElement('input');
    useAIFallbackCheckbox.type = 'checkbox';
    useAIFallbackCheckbox.checked = aiConfig.useAIFallback;
    useAIFallbackCheckbox.addEventListener('change', () => {
        aiConfig.useAIFallback = useAIFallbackCheckbox.checked;
    });
    aiSettingsContainer.appendChild(useAIFallbackCheckbox);

    // 是否将触发词也发送给用户
    const hideTriggerWordsLabel = document.createElement('label');
    hideTriggerWordsLabel.style.display = 'block';
    hideTriggerWordsLabel.style.marginTop = '10px';
    hideTriggerWordsLabel.textContent = '是否将带方括号的触发词也发送给用户？(默认否)';
    aiSettingsContainer.appendChild(hideTriggerWordsLabel);

    const hideTriggerWordsCheckbox = document.createElement('input');
    hideTriggerWordsCheckbox.type = 'checkbox';
    hideTriggerWordsCheckbox.checked = aiConfig.sendTriggerWordsToUser;
    hideTriggerWordsCheckbox.addEventListener('change', () => {
        aiConfig.sendTriggerWordsToUser = hideTriggerWordsCheckbox.checked;
    });
    aiSettingsContainer.appendChild(hideTriggerWordsCheckbox);

    configContainer.appendChild(aiSettingsContainer);

    /************************************************************
     * 6. 底部按钮行
     ************************************************************/
    const bottomBtnContainer = document.createElement('div');
    bottomBtnContainer.style.display = 'flex';
    bottomBtnContainer.style.justifyContent = 'space-between';
    bottomBtnContainer.style.alignItems = 'center';

    // 重置当前分组
    const resetBtn = document.createElement('button');
    resetBtn.textContent = '重置当前分组';
    Object.assign(resetBtn.style, {
        backgroundColor: 'orange',
        color: '#fff',
        border: 'none',
        borderRadius: '5px',
        padding: '5px 10px',
        cursor: 'pointer'
    });
    resetBtn.addEventListener('click', () => {
        if (confirm('确认清空当前分组的关键词回复列表吗？此操作不可撤销。')) {
            aiConfig.categories[currentCategoryIndex].keywords = [];
            renderKeywords();
        }
    });
    bottomBtnContainer.appendChild(resetBtn);

    // 保存配置
    const saveBtn = document.createElement('button');
    saveBtn.textContent = '保存配置';
    Object.assign(saveBtn.style, {
        background: '#1966ff',
        color: '#fff',
        border: 'none',
        borderRadius: '5px',
        padding: '5px 10px',
        cursor: 'pointer'
    });
    saveBtn.addEventListener('click', () => {
        saveConfigToStorage();
            showStatusMessage("配置已保存", "green");
    });
    bottomBtnContainer.appendChild(saveBtn);

    // 导出配置
    const exportBtn = document.createElement('button');
    exportBtn.textContent = '导出配置';
    Object.assign(exportBtn.style, {
        backgroundColor: '#17a2b8',
        color: '#fff',
        border: 'none',
        borderRadius: '5px',
        padding: '5px 10px',
        cursor: 'pointer',
        marginRight: '10px'
    });
    exportBtn.addEventListener('click', async () => {
        try {
            // 准备要导出的配置
            const exportConfig = JSON.parse(JSON.stringify(aiConfig));

            // 导出选项容器
            const exportOptionsWindow = document.createElement('div');
            Object.assign(exportOptionsWindow.style, {
                position: 'fixed',
                top: '0',
                left: '0',
                width: '100%',
                height: '100%',
                backgroundColor: 'rgba(0,0,0,0.5)',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                zIndex: 10000
            });

            const exportOptionsBox = document.createElement('div');
            Object.assign(exportOptionsBox.style, {
                width: '500px',
                backgroundColor: '#fff',
                borderRadius: '5px',
                padding: '20px',
                boxShadow: '0 0 10px rgba(0,0,0,0.2)'
            });

            const exportOptionsTitle = document.createElement('h3');
            exportOptionsTitle.textContent = '导出选项';
            exportOptionsTitle.style.marginTop = '0';
            exportOptionsBox.appendChild(exportOptionsTitle);

            // 添加导出选项
            const includeMediaOption = document.createElement('div');
            includeMediaOption.style.marginBottom = '15px';

            const includeMediaCheckbox = document.createElement('input');
            includeMediaCheckbox.type = 'checkbox';
            includeMediaCheckbox.id = 'includeMedia';
            includeMediaCheckbox.checked = true;

            const includeMediaLabel = document.createElement('label');
            includeMediaLabel.htmlFor = 'includeMedia';
            includeMediaLabel.textContent = '包含媒体文件（图片和视频）';
            includeMediaLabel.style.marginLeft = '5px';

            includeMediaOption.appendChild(includeMediaCheckbox);
            includeMediaOption.appendChild(includeMediaLabel);
            exportOptionsBox.appendChild(includeMediaOption);

            // 媒体文件大小警告
            const mediaWarning = document.createElement('div');
            mediaWarning.style.color = 'orange';
            mediaWarning.style.marginBottom = '15px';
            mediaWarning.textContent = '警告：包含媒体文件可能会导致配置文件非常大，不建议在自动导入场景使用。';
            exportOptionsBox.appendChild(mediaWarning);

            // 按钮容器
            const btnContainer = document.createElement('div');
            Object.assign(btnContainer.style, {
                display: 'flex',
                justifyContent: 'flex-end',
                marginTop: '20px'
            });

            // 取消按钮
            const cancelBtn = document.createElement('button');
            cancelBtn.textContent = '取消';
            Object.assign(cancelBtn.style, {
                backgroundColor: '#6c757d',
                color: '#fff',
                border: 'none',
                borderRadius: '5px',
                padding: '5px 15px',
                cursor: 'pointer',
                marginRight: '10px'
            });
            cancelBtn.addEventListener('click', () => {
                document.body.removeChild(exportOptionsWindow);
            });
            btnContainer.appendChild(cancelBtn);

            // 确认导出按钮
            const confirmExportBtn = document.createElement('button');
            confirmExportBtn.textContent = '导出';
            Object.assign(confirmExportBtn.style, {
                backgroundColor: '#28a745',
                color: '#fff',
                border: 'none',
                borderRadius: '5px',
                padding: '5px 15px',
                cursor: 'pointer'
            });

            confirmExportBtn.addEventListener('click', async () => {
                // 显示加载状态
                confirmExportBtn.textContent = '处理中...';
                confirmExportBtn.disabled = true;

                try {
                    // 根据选项处理媒体文件
                    if (includeMediaCheckbox.checked) {
                        // 需要包含媒体文件，将ID替换为实际数据
                        for (const category of exportConfig.categories) {
                            for (const keyword of category.keywords) {
                                // 处理图片
                                if (keyword.images && keyword.images.length > 0) {
                                    const imagePromises = keyword.images.map(async (imageId) => {
                                        return await loadMediaFromDB(imageId);
                                    });
                                    keyword.images = await Promise.all(imagePromises);
                                    keyword.images = keyword.images.filter(img => img !== null);
                                }

                                // 处理视频
                                if (keyword.videos && keyword.videos.length > 0) {
                                    const videoPromises = keyword.videos.map(async (videoId) => {
                                        return await loadMediaFromDB(videoId);
                                    });
                                    keyword.videos = await Promise.all(videoPromises);
                                    keyword.videos = keyword.videos.filter(vid => vid !== null);
                                }
                            }
                        }

                        console.log("[AI监控] 导出配置(包含媒体文件)");
                    } else {
                        // 不包含媒体文件，则删除所有媒体数据
                        for (const category of exportConfig.categories) {
                            for (const keyword of category.keywords) {
                                if (keyword.images) keyword.images = [];
                                if (keyword.videos) keyword.videos = [];
                            }
                        }
                        console.log("[AI监控] 导出配置(不含媒体文件)");
                    }

                    // 导出配置
                    const exportData = JSON.stringify(exportConfig, null, 2);

                    // 创建导出窗口
            const exportWindow = document.createElement('div');
            Object.assign(exportWindow.style, {
                position: 'fixed',
                top: '0',
                left: '0',
                width: '100%',
                height: '100%',
                backgroundColor: 'rgba(0,0,0,0.5)',
                display: 'flex',
                justifyContent: 'center',
                        alignItems: 'center',
                zIndex: 10000
            });

            const contentDiv = document.createElement('div');
            Object.assign(contentDiv.style, {
                background: '#fff',
                padding: '20px',
                borderRadius: '10px',
                boxShadow: '0 0 10px rgba(0,0,0,0.3)',
                        maxWidth: '80%',
                        width: '100%',
                        maxHeight: '80vh',
                        overflow: 'auto'
            });

            const textarea = document.createElement('textarea');
            textarea.style.width = '100%';
                    textarea.style.height = '300px';
                    textarea.style.marginBottom = '10px';
                    textarea.style.fontFamily = 'monospace';
                    textarea.value = exportData;
            contentDiv.appendChild(textarea);

            const closeBtn = document.createElement('button');
            closeBtn.textContent = '关闭';
            Object.assign(closeBtn.style, {
                marginTop: '10px',
                        backgroundColor: '#dc3545',
                color: '#fff',
                border: 'none',
                borderRadius: '5px',
                padding: '5px 10px',
                cursor: 'pointer'
            });
            closeBtn.addEventListener('click', () => {
                document.body.removeChild(exportWindow);
            });
            contentDiv.appendChild(closeBtn);

            exportWindow.appendChild(contentDiv);
            document.body.appendChild(exportWindow);

                    // 关闭导出选项窗口
                    document.body.removeChild(exportOptionsWindow);
                } catch (error) {
                    console.error('[AI监控] 导出配置失败:', error);
                    alert('导出失败: ' + error.message);
                    confirmExportBtn.textContent = '导出';
                    confirmExportBtn.disabled = false;
                }
            });

            btnContainer.appendChild(confirmExportBtn);
            exportOptionsBox.appendChild(btnContainer);

            exportOptionsWindow.appendChild(exportOptionsBox);
            document.body.appendChild(exportOptionsWindow);
        } catch (e) {
            aiLogger.error(`[AI监控] 导出配置时出错：${e.message}`);
            showStatusMessage("导出配置失败", "red");
        }
    });

    // 导入配置
    const importBtn = document.createElement('button');
    importBtn.textContent = '导入配置';
    Object.assign(importBtn.style, {
        backgroundColor: '#6c757d',
        color: '#fff',
        border: 'none',
        borderRadius: '5px',
        padding: '5px 10px',
        cursor: 'pointer',
        marginRight: '10px'
    });
    importBtn.addEventListener('click', () => {
        const importWindow = document.createElement('div');
        Object.assign(importWindow.style, {
            position: 'fixed',
            top: '0',
            left: '0',
            width: '100%',
            height: '100%',
            backgroundColor: 'rgba(0,0,0,0.5)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 10000
        });

        const contentDiv = document.createElement('div');
        Object.assign(contentDiv.style, {
            background: '#fff',
            padding: '20px',
            borderRadius: '10px',
            boxShadow: '0 0 10px rgba(0,0,0,0.3)',
            maxWidth: '500px',
            width: '100%'
        });

        const descP = document.createElement('p');
        descP.textContent = '请在下方粘贴导出的配置JSON，然后点击导入：';
        contentDiv.appendChild(descP);

        const textarea = document.createElement('textarea');
        textarea.style.width = '100%';
        textarea.style.height = '200px';
        contentDiv.appendChild(textarea);

        const btnContainer = document.createElement('div');
        btnContainer.style.marginTop = '10px';
        btnContainer.style.textAlign = 'right';

        const confirmImportBtn = document.createElement('button');
        confirmImportBtn.textContent = '导入';
        Object.assign(confirmImportBtn.style, {
            backgroundColor: '#28a745',
            color: '#fff',
            border: 'none',
            borderRadius: '5px',
            padding: '5px 10px',
            cursor: 'pointer',
            marginRight: '10px'
        });
        confirmImportBtn.addEventListener('click', async () => {
            const jsonStr = textarea.value.trim();
            if (!jsonStr) {
                alert('请输入配置JSON');
                return;
            }

            // 显示处理中状态
            confirmImportBtn.textContent = '处理中...';
            confirmImportBtn.disabled = true;

            try {
                const parsed = JSON.parse(jsonStr);
                if (typeof parsed === 'object' && parsed.categories && parsed.fallbackReply !== undefined) {
                    // 处理可能包含的媒体文件，将其存储到IndexedDB
                    for (const category of parsed.categories) {
                        for (const keyword of category.keywords) {
                            // 处理图片
                            if (keyword.images && keyword.images.length > 0) {
                                const newImageIds = [];
                                for (const imageData of keyword.images) {
                                    // 跳过已经是ID格式的媒体文件引用
                                    if (typeof imageData === 'string' && imageData.startsWith('media_')) {
                                        newImageIds.push(imageData);
                                        continue;
                                    }

                                    try {
                                        // 保存到IndexedDB并获取ID
                                        const mediaId = await saveMediaToDB(imageData, 'image');
                                        newImageIds.push(mediaId);
                                    } catch (error) {
                                        console.error('[AI监控] 导入图片保存失败:', error);
                                        // 如果无法保存到IndexedDB，保留原始数据
                                        newImageIds.push(imageData);
                                    }
                                }
                                keyword.images = newImageIds;
                            }

                            // 处理视频
                            if (keyword.videos && keyword.videos.length > 0) {
                                const newVideoIds = [];
                                for (const videoData of keyword.videos) {
                                    // 跳过已经是ID格式的媒体文件引用
                                    if (typeof videoData === 'string' && videoData.startsWith('media_')) {
                                        newVideoIds.push(videoData);
                                        continue;
                                    }

                                    try {
                                        // 保存到IndexedDB并获取ID
                                        const mediaId = await saveMediaToDB(videoData, 'video');
                                        newVideoIds.push(mediaId);
                                    } catch (error) {
                                        console.error('[AI监控] 导入视频保存失败:', error);
                                        // 如果无法保存到IndexedDB，保留原始数据
                                        newVideoIds.push(videoData);
                                    }
                                }
                                keyword.videos = newVideoIds;
                            }
                        }
                    }

                    // 更新配置并保存
                    aiConfig = parsed;
                    saveConfigToStorage();

                    // 更新UI
                    renderCategoryNav();
                    renderKeywords();
                    fallbackInput.value = aiConfig.fallbackReply;
                    apiUrlInput.value = aiConfig.apiURL || '';
                    appIdInput.value = aiConfig.applicationID || '';
                    appAuthInput.value = aiConfig.appAuthorization || '';
                    useAIFallbackCheckbox.checked = aiConfig.useAIFallback || false;
                    sendTriggerWordsCheckbox.checked = aiConfig.sendTriggerWordsToUser || false;

                    document.body.removeChild(importWindow);
                    showStatusMessage("配置已导入并保存", "green");
                } else {
                    alert('无效的配置格式');
                    confirmImportBtn.textContent = '导入';
                    confirmImportBtn.disabled = false;
                }
            } catch (e) {
                alert('解析配置时出错: ' + e.message);
                confirmImportBtn.textContent = '导入';
                confirmImportBtn.disabled = false;
            }
        });
        btnContainer.appendChild(confirmImportBtn);

        contentDiv.appendChild(btnContainer);
        importWindow.appendChild(contentDiv);
        document.body.appendChild(importWindow);
    });
    bottomBtnContainer.appendChild(importBtn);

    configContainer.appendChild(bottomBtnContainer);

    // 配置面板插入页面
    document.body.appendChild(configContainer);

    /************************************************************
     * 7. 额外按钮：查看特殊按钮触发词
     ************************************************************/
    const showSpecialTriggersBtn = document.createElement('button');
    showSpecialTriggersBtn.textContent = '查看特殊按钮触发词';
    Object.assign(showSpecialTriggersBtn.style, {
        position: 'absolute',
        top: '10px',
        right: '10px',
        zIndex: 9999,
        backgroundColor: '#444',
        color: '#fff',
        border: 'none',
        borderRadius: '5px',
        padding: '5px 10px',
        cursor: 'pointer',
        fontSize: '12px'
    });
    showSpecialTriggersBtn.addEventListener('click', () => {
        showSpecialTriggersOverlay();
    });
    configContainer.appendChild(showSpecialTriggersBtn);

    function showSpecialTriggersOverlay() {
        const overlay = document.createElement('div');
        Object.assign(overlay.style, {
            position: 'fixed',
            top: '0',
            left: '0',
            width: '100%',
            height: '100%',
            backgroundColor: 'rgba(0,0,0,0.5)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 10000
        });

        const content = document.createElement('div');
        Object.assign(content.style, {
            background: '#fff',
            padding: '20px',
            borderRadius: '10px',
            boxShadow: '0 0 10px rgba(0,0,0,0.3)',
            minWidth: '300px'
        });

        const title = document.createElement('h3');
        title.textContent = '特殊客服回复触发词';
        content.appendChild(title);

        specialSendTriggers.forEach(item => {
            const p = document.createElement('p');
            p.textContent = `当自动回复文本中包含 "${item.triggerText}" → 点击第 ${item.btnIndex + 1} 个订单"发送"按钮`;
            content.appendChild(p);
        });

        const tip = document.createElement('p');
        tip.style.color = 'red';
        tip.textContent = '注意：若"是否将触发词也发送给用户"未勾选，则不会真正发给用户，但仍能触发按钮点击。';
        content.appendChild(tip);

        const closeBtn = document.createElement('button');
        closeBtn.textContent = '关闭';
        Object.assign(closeBtn.style, {
            backgroundColor: '#1966ff',
            color: '#fff',
            border: 'none',
            borderRadius: '5px',
            padding: '5px 10px',
            cursor: 'pointer'
        });
        closeBtn.addEventListener('click', () => {
            document.body.removeChild(overlay);
        });
        content.appendChild(closeBtn);

        overlay.appendChild(content);
        document.body.appendChild(overlay);
    }

    // 渲染初始分类与关键词
    renderCategoryNav();
    renderKeywords();

    /************************************************************
     * 8. 自动回复核心逻辑
     ************************************************************/
    function getMatchedCategoryForOrder(orderInfo) {
        if (!orderInfo || orderInfo.noOrders || !orderInfo.orders || orderInfo.orders.length === 0) {
            return aiConfig.categories[0];
        }
        const allNames = orderInfo.orders.map(o => o.name).join(" ");

        // 遍历分类组，查找匹配的分类组
        for (let i = 0; i < aiConfig.categories.length; i++) {
            // 跳过默认分类
            if (aiConfig.categories[i].isDefault) continue;
            // 如果匹配，则使用该分类组
            if (allNames.includes(aiConfig.categories[i].keyword)) {
                return aiConfig.categories[i];
            }
        }
        // 如果没有匹配到，返回默认分类组
        return aiConfig.categories.find(c => c.isDefault) || aiConfig.categories[0];
    }

    const getAutoReplyMessage = (lastUserMessage, matchedKeywordConfig) => {
        if (!matchedKeywordConfig) return aiConfig.fallbackReply;

        // 简化处理逻辑，统一构建回复对象
        const result = {
            text: matchedKeywordConfig.text || '',
            images: matchedKeywordConfig.images || [],
            videos: matchedKeywordConfig.videos || []
        };

        // 检查是否只有文本（没有图片和视频）
        if (result.images.length === 0 && result.videos.length === 0) {
            // 如果只有文本，直接返回文本字符串
            return result.text || aiConfig.fallbackReply;
        }

        // 否则返回完整对象
        return result;
    };

    let isReplying = false;
    const autoReplyToWaitReplyUsers = () => {
        if (isReplying) return;

        let usersToReply = {};
        statusesToAutoReply.forEach(statusKey => {
            if (lastStatusMap[statusKey] && lastStatusMap[statusKey].users) {
                Object.assign(usersToReply, lastStatusMap[statusKey].users);
            }
        });

        const usernames = Object.keys(usersToReply);
        if (usernames.length === 0) return;

        for (const uname of usernames) {
            if (uname === lastSelectedUser && lastChatMessages && lastChatMessages.length > 0) {
                // 当前选中用户
                const userMessages = lastChatMessages.filter(m => m.sender === '用户');
                if (userMessages.length === 0) continue;
                const lastUserMessageObj = userMessages[userMessages.length - 1];
                const lastUserMsg = lastUserMessageObj.content;
                const lastUserMsgTime = lastUserMessageObj.time || "";

                const lastReplied = repliedMessagesMap[uname];
                if (lastReplied && lastReplied.text === lastUserMsg && lastReplied.time === lastUserMsgTime) {
                    // 已经回复过这句话
                    continue;
                }

                const category = getMatchedCategoryForOrder(lastOrderInfo);
                let matchedConfig = null;
                for (const kw of category.keywords) {
                    if (kw.keyword && lastUserMsg.includes(kw.keyword)) {
                        matchedConfig = kw;
                        break;
                    }
                }

                if (!matchedConfig) {
                    // 无关键词匹配 → AI
                    if (aiConfig.useAIFallback && aiConfig.apiURL && aiConfig.applicationID && aiConfig.appAuthorization) {
                        isReplying = true;
                        getAIResponse(lastUserMsg, lastChatMessages, lastOrderInfo).then(responseText => {
                            const replyMsg = responseText || aiConfig.fallbackReply;
                            sendMessageToUser(replyMsg, () => {
                                repliedMessagesMap[uname] = { text: lastUserMsg, time: lastUserMsgTime };
                                isReplying = false;
                            });
                        }).catch(err => {
                            aiLogger.error("[AI监控] AI接口调用失败：" + err.message);
                            isReplying = true;
                            sendMessageToUser(aiConfig.fallbackReply, () => {
                                repliedMessagesMap[uname] = { text: lastUserMsg, time: lastUserMsgTime };
                                isReplying = false;
                            });
                        });
                        break;
                    } else {
                        matchedConfig = {
                            replyType: 'text',
                            text: aiConfig.fallbackReply
                        };
                    }
                }

                if (matchedConfig) {
                    const replyMsg = getAutoReplyMessage(lastUserMsg, matchedConfig);
                    if (!replyMsg) continue;

                    isReplying = true;
                    sendMessageToUser(replyMsg, () => {
                        repliedMessagesMap[uname] = { text: lastUserMsg, time: lastUserMsgTime };
                        isReplying = false;
                    });
                }
                break;
            } else {
                // 若不是当前选中用户，需要先点进去，获取消息再回复
                isReplying = true;
                clickUserInWaitReply(uname, () => {
                    const checkInterval = setInterval(() => {
                        if (lastSelectedUser === uname && lastChatMessages && lastChatMessages.length > 0) {
                            clearInterval(checkInterval);
                            const userMessages = lastChatMessages.filter(m => m.sender === '用户');
                            if (userMessages.length === 0) {
                                isReplying = false;
                                return;
                            }
                            const lastUserMessageObj = userMessages[userMessages.length - 1];
                            const lastUserMsg = lastUserMessageObj.content;
                            const lastUserMsgTime = lastUserMessageObj.time || "";

                            const lastReplied = repliedMessagesMap[uname];
                            if (lastReplied && lastReplied.text === lastUserMsg && lastReplied.time === lastUserMsgTime) {
                                isReplying = false;
                                return;
                            }

                            const category = getMatchedCategoryForOrder(lastOrderInfo);
                            let matchedConfig = null;
                            for (const kw of category.keywords) {
                                if (kw.keyword && lastUserMsg.includes(kw.keyword)) {
                                    matchedConfig = kw;
                                    break;
                                }
                            }

                            if (!matchedConfig) {
                                // 无关键词 → AI
                                if (aiConfig.useAIFallback && aiConfig.apiURL && aiConfig.applicationID && aiConfig.appAuthorization) {
                                    getAIResponse(lastUserMsg, lastChatMessages, lastOrderInfo).then(responseText => {
                                        const replyMsg = responseText || aiConfig.fallbackReply;
                                        sendMessageToUser(replyMsg, () => {
                                            repliedMessagesMap[uname] = { text: lastUserMsg, time: lastUserMsgTime };
                                            isReplying = false;
                                        });
                                    }).catch(err => {
                                        aiLogger.error("[AI监控] AI接口调用失败：" + err.message);
                                        sendMessageToUser(aiConfig.fallbackReply, () => {
                                            repliedMessagesMap[uname] = { text: lastUserMsg, time: lastUserMsgTime };
                                            isReplying = false;
                                        });
                                    });
                                } else {
                                    matchedConfig = {
                                        replyType: 'text',
                                        text: aiConfig.fallbackReply
                                    };
                                    const replyMsg = getAutoReplyMessage(lastUserMsg, matchedConfig);
                                    sendMessageToUser(replyMsg, () => {
                                        repliedMessagesMap[uname] = { text: lastUserMsg, time: lastUserMsgTime };
                                        isReplying = false;
                                    });
                                }
                            } else {
                                const replyMsg = getAutoReplyMessage(lastUserMsg, matchedConfig);
                                sendMessageToUser(replyMsg, () => {
                                    repliedMessagesMap[uname] = { text: lastUserMsg, time: lastUserMsgTime };
                                    isReplying = false;
                                });
                            }
                        }
                    }, 500);
                });
                break;
            }
        }
    };

    const clickUserInWaitReply = (username, callback) => {
        const chatArea = document.querySelector("#chantListScrollArea");
        if (!chatArea) {
            callback();
            return;
        }
        const chatListContainer = chatArea.querySelector("[class*='ReactVirtualized__Grid__innerScrollContainer']");
        if (!chatListContainer) {
            callback();
            return;
        }
        const userItems = chatListContainer.querySelectorAll('[data-btm-id]');
        for (const item of userItems) {
            const { username: uname } = getUsernameAndMessage(item);
            if (uname === username) {
                item.click();
                setTimeout(() => {
                    callback();
                }, 500);
                break;
            }
        }
    };

    /************************************************************
     * 9. 修改 sendMessageToUser，让其检测并点击特殊触发按钮
     ************************************************************/
    function sendMessageToUser(message, doneCallback) {
        const textarea = document.querySelector('textarea[data-qa-id="qa-send-message-textarea"]');
        const sendBtn = document.querySelector('div[data-qa-id="qa-send-message-button"]');

        if (!textarea || !sendBtn) {
            doneCallback();
            return;
        }

        if (typeof message === 'string') {
            // 纯文本
            const rawText = message;
            // 若不想让触发词发给用户，则去掉
            const finalText = aiConfig.sendTriggerWordsToUser ? rawText : removeSpecialTriggerWords(rawText);

            setTextareaValue(textarea, finalText);
            setTimeout(() => {
                sendBtn.click();
                setTimeout(() => {
                    checkAndClickSpecialSend(rawText); // 检查触发词后点按钮
                    doneCallback();
                }, 500);
            }, 500);

        } else if (typeof message === 'object') {
            // 包含 text / images / videos
            const { text, images, videos } = message;
            const rawText = text || "";
            const finalText = aiConfig.sendTriggerWordsToUser ? rawText : removeSpecialTriggerWords(rawText);

            if (finalText) {
                setTextareaValue(textarea, finalText);
                setTimeout(() => {
                    sendBtn.click();
                    setTimeout(() => {
                        sendFiles(images, videos, () => {
                            checkAndClickSpecialSend(rawText);
                            doneCallback();
                        });
                    }, 500);
                }, 500);
            } else {
                // 没有文本，仅发图或视频
                sendFiles(images, videos, () => {
                    checkAndClickSpecialSend(rawText);
                    doneCallback();
                });
            }
        }
    }

    function setTextareaValue(textarea, val) {
        const nativeInputValueSetter = Object.getOwnPropertyDescriptor(window.HTMLTextAreaElement.prototype, 'value').set;
        nativeInputValueSetter.call(textarea, val);
        const inputEvent = new Event('input', { bubbles: true });
        textarea.dispatchEvent(inputEvent);
    }

    // 去除触发词
    function removeSpecialTriggerWords(msg) {
        let res = msg;
        specialSendTriggers.forEach(item => {
            res = res.replaceAll(item.triggerText, "");
        });
        return res;
    }

    // 检查自动回复中是否包含特殊词
    function checkAndClickSpecialSend(rawReply) {
        for (const item of specialSendTriggers) {
            if (rawReply.includes(item.triggerText)) {
                clickOrderSendButtonByIndex(item.btnIndex);
            }
        }
    }

    // 点击订单区域第n个"发送"按钮
    function clickOrderSendButtonByIndex(btnIndex) {
        const allSendSpans = document.querySelectorAll(
            '#mona-workbench_订单 button.ecom-btn.ecom-btn-link.ecom-btn-sm.orderOptions span'
        );
        if (allSendSpans.length > btnIndex) {
            allSendSpans[btnIndex].click();
            aiLogger.log("[AI监控] 已点击第", btnIndex + 1, "个订单发送按钮 =>", allSendSpans[btnIndex].textContent);
        } else {
            aiLogger.warn("[AI监控] 未找到下标为", btnIndex, "的订单发送按钮");
        }
    }

    // 发送图片或视频
    const sendFiles = async (images, videos, doneCallback) => {
        try {
            // 合并所有媒体文件到一个队列
            let mediaQueue = [];

            // 从数据库加载图片
        if (images && images.length > 0) {
                const loadedImages = await loadMediaBatch(images);
                loadedImages.forEach((imgData, index) => {
                    if (imgData) {
                        mediaQueue.push({
                            base64: imgData,
                            type: 'image/png'
                        });
                    } else {
                        console.warn(`[AI监控] 无法加载图片: ${images[index]}`);
                    }
                });
            }

            // 从数据库加载视频
        if (videos && videos.length > 0) {
                const loadedVideos = await loadMediaBatch(videos);
                loadedVideos.forEach((vidData, index) => {
                    if (vidData) {
                        mediaQueue.push({
                            base64: vidData,
                            type: 'video/mp4'
                        });
                    } else {
                        console.warn(`[AI监控] 无法加载视频: ${videos[index]}`);
                    }
                });
            }

            // 没有媒体文件需要发送
            if (mediaQueue.length === 0) {
            doneCallback();
                return;
            }

            console.log("[AI监控] 准备发送媒体队列，共", mediaQueue.length, "个文件");

            // 按顺序处理媒体文件
            let currentIndex = 0;

            // 定义处理单个媒体文件的函数
            const processNextMedia = () => {
                if (currentIndex >= mediaQueue.length) {
                    console.log("[AI监控] 所有媒体文件处理完成");
                    doneCallback();
                    return;
                }

                const currentItem = mediaQueue[currentIndex];
                console.log("[AI监控] 开始处理第", currentIndex + 1, "个媒体文件");

                // 清理上一个可能存在的观察器
                if (window.currentMediaObserver) {
                    window.currentMediaObserver.disconnect();
                    window.currentMediaObserver = null;
                }

                // 创建新的观察器
                const observer = new MutationObserver((mutations) => {
                    const fileSendBtn = document.querySelector('button[data-qa-id="qa-send-file-popup-submit"]');

                    if (fileSendBtn) {
                        console.log("[AI监控] 找到文件发送按钮，准备点击");

                        // 断开观察器
                        observer.disconnect();
                        window.currentMediaObserver = null;

                        // 点击发送按钮
                        fileSendBtn.click();
                        console.log("[AI监控] 已点击发送按钮");

                        // 处理下一个媒体文件
                        currentIndex++;
                        setTimeout(processNextMedia, 1500);
                    }
                });

                // 保存当前观察器的引用
                window.currentMediaObserver = observer;

                // 开始观察DOM变化
                observer.observe(document.body, {
                    childList: true,
                    subtree: true
                });

                // 注入当前媒体文件
        const textarea = document.querySelector('textarea[data-qa-id="qa-send-message-textarea"]');
                if (!textarea) {
                    console.log("[AI监控] 未找到文本输入区域，跳过当前媒体");
                    currentIndex++;
                    setTimeout(processNextMedia, 500);
                    return;
                }

                try {
                    // 解析并注入文件
                    console.log("[AI监控] 拖拽媒体文件到输入框");
                    injectMediaToTextarea(currentItem.base64, currentItem.type, textarea);
                } catch (error) {
                    console.error("[AI监控] 媒体文件注入失败:", error);
                    currentIndex++;
                    setTimeout(processNextMedia, 500);
                }

                // 设置超时保护
                setTimeout(() => {
                    if (window.currentMediaObserver === observer) {
                        console.log("[AI监控] 处理当前媒体超时，继续下一个");
                        observer.disconnect();
                        window.currentMediaObserver = null;
                        currentIndex++;
                        processNextMedia();
                    }
                }, 10000);
            };

            // 开始处理第一个媒体文件
            processNextMedia();
        } catch (error) {
            console.error("[AI监控] 发送媒体文件出错:", error);
            doneCallback(); // 确保即使出错也能继续流程
        }
    };

    // 注入媒体文件到文本区域
    function injectMediaToTextarea(base64Data, mimeType, textarea) {
        try {
            // 检查输入
            if (!base64Data || !base64Data.includes('base64')) {
                console.error("[AI监控] 无效的base64数据");
                return false;
            }

            // 转换base64为文件对象
        const byteString = atob(base64Data.split(',')[1]);
        const ab = new ArrayBuffer(byteString.length);
        const ia = new Uint8Array(ab);
        for (let i = 0; i < byteString.length; i++) {
            ia[i] = byteString.charCodeAt(i);
        }

        const blob = new Blob([ab], { type: mimeType });
        const file = new File([blob], mimeType.startsWith('image/') ? 'image.png' : 'video.mp4', { type: mimeType });

            // 创建DataTransfer对象
        const dataTransfer = new DataTransfer();
        dataTransfer.items.add(file);

            // 模拟拖放事件
        const dragEnter = new DragEvent('dragenter', { bubbles: true, cancelable: true, dataTransfer });
        textarea.dispatchEvent(dragEnter);

        const dragOver = new DragEvent('dragover', { bubbles: true, cancelable: true, dataTransfer });
        textarea.dispatchEvent(dragOver);

        const drop = new DragEvent('drop', { bubbles: true, cancelable: true, dataTransfer });
        textarea.dispatchEvent(drop);

            return true;
        } catch (error) {
            console.error("[AI监控] 注入媒体文件时出错:", error);
            return false;
        }
    }

    /************************************************************
     * 10. AI 接口请求逻辑
     ************************************************************/
    function getAIResponse(userMessage, chatMessages, orderInfo) {
        return new Promise((resolve, reject) => {
            if (!aiConfig.apiURL || !aiConfig.applicationID || !aiConfig.appAuthorization) {
                return reject(new Error("AI配置不完整"));
            }

            let orderText = '';
            if (orderInfo && !orderInfo.noOrders && orderInfo.orders && orderInfo.orders.length > 0) {
                orderText = orderInfo.orders.map(o => {
                    return `订单${o.orderNumber}：商品名="${o.name}" 状态="${o.status}"`;
                }).join('\n');
            } else {
                orderText = '暂无相关订单信息';
            }

            const prompt =
`以下是用户的订单信息：
${orderText}

用户最新的问题是："${userMessage}"

你是抖店客服AI聊天助手，是一个抖店客服智能体，负责为用户提供服务，解答关于商品信息、订单状态、修改地址等问题，因为所有的订单都是用户在抖音超级福袋活动中奖所得，都是0元支付免费赠送给用户的，是不支持取消、退货、退款、换货的，所以绝不能提及退款、退货、换货话术，用户兑奖后订单就无法取消的，并且用户兑奖后订单就会显示出来，所以奖品会在48小时内给用户安排发出，并且也不支持折现。`;

            const openUrl = aiConfig.apiURL + "/application/" + aiConfig.applicationID + "/chat/open";

            // 先获取 chat_id
            GM_xmlhttpRequest({
                method: "GET",
                url: openUrl,
                headers: {
                    "AUTHORIZATION": aiConfig.appAuthorization,
                    "accept": "application/json"
                },
                onload: (openRes) => {
                    if (openRes.status === 200) {
                        let openData = JSON.parse(openRes.responseText);
                        if (openData.code === 200 && openData.data) {
                            const chatId = openData.data;
                            const chatUrl = aiConfig.apiURL + "/application/chat_message/" + chatId;

                            // 再向 chat_message 发消息
                            GM_xmlhttpRequest({
                                method: "POST",
                                url: chatUrl,
                                headers: {
                                    "AUTHORIZATION": aiConfig.appAuthorization,
                                    "accept": "application/json",
                                    "Content-Type": "application/json"
                                },
                                data: JSON.stringify({
                                    message: prompt,
                                    re_chat: false,
                                    stream: false
                                }),
                                onload: (chatRes) => {
                                    if (chatRes.status === 200) {
                                        let chatData = JSON.parse(chatRes.responseText);
                                        if (chatData.code === 200 && chatData.data && chatData.data.content) {
                                            resolve(chatData.data.content);
                                        } else {
                                            reject(new Error("AI回复格式错误或无内容"));
                                        }
                                    } else {
                                        reject(new Error("AI接口请求失败，状态码：" + chatRes.status));
                                    }
                                },
                                onerror: (err) => {
                                    reject(new Error("AI接口请求错误：" + JSON.stringify(err)));
                                }
                            });
                        } else {
                            reject(new Error("获取chat_id失败：" + openRes.responseText));
                        }
                    } else {
                        reject(new Error("获取chat_id接口请求失败，状态码：" + openRes.status));
                    }
                },
                onerror: (err) => {
                    reject(new Error("获取chat_id接口请求错误：" + JSON.stringify(err)));
                }
            });
        });
    }

    /************************************************************
     * 11. 启动轮询、监控
     ************************************************************/
    console.clear();
    aiLogger.log("%c[AI监控] 抖店飞鸽AI自动聊天监控 - 上下文AI+特殊触发词版", "font-weight:bold;color:black;");
    aiLogger.log("%c[AI监控] 脚本已启动，等待页面加载...", "color:black;");
    showStatusMessage("脚本已启动，等待页面加载...", "blue");

    pollForChatList();
    monitorOrderInfo();

    /************************************************************
     * 9. 插入"AI配置"按钮
     ************************************************************/
    const configToggleBtn = document.createElement('button');
    configToggleBtn.textContent = 'AI配置';
    Object.assign(configToggleBtn.style, {
        position: 'fixed',
        bottom: '10px',
        left: '50%',
        transform: 'translateX(-50%)',
        zIndex: 9999,
        background: '#1966ff',
        color: '#fff',
        border: 'none',
        borderRadius: '5px',
        padding: '5px 10px',
        cursor: 'pointer'
    });
    configToggleBtn.addEventListener('click', () => {
        configContainer.style.display = (configContainer.style.display === 'none' ? 'block' : 'none');
    });
    document.body.appendChild(configToggleBtn);

})();
