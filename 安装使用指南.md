# 抖店飞鸽AI自动聊天监控 - 安装使用指南

## 快速开始

### 第一步：安装Tampermonkey扩展

#### Chrome浏览器
1. 打开Chrome浏览器
2. 访问 [Chrome网上应用店](https://chrome.google.com/webstore)
3. 搜索"Tampermonkey"
4. 点击"添加至Chrome"
5. 在弹出窗口中点击"添加扩展程序"

#### Firefox浏览器
1. 打开Firefox浏览器
2. 访问 [Firefox附加组件](https://addons.mozilla.org)
3. 搜索"Tampermonkey"
4. 点击"添加到Firefox"
5. 点击"安装"确认

#### Edge浏览器
1. 打开Microsoft Edge浏览器
2. 访问 [Edge加载项商店](https://microsoftedge.microsoft.com/addons)
3. 搜索"Tampermonkey"
4. 点击"获取"
5. 点击"添加扩展"

### 第二步：安装脚本

1. **打开Tampermonkey管理面板**
   - 点击浏览器右上角的Tampermonkey图标
   - 选择"管理面板"

2. **创建新脚本**
   - 点击"新建脚本"按钮
   - 删除默认的模板代码

3. **复制脚本代码**
   - 打开项目中的`Tampermonkey.js`文件
   - 全选并复制所有代码（Ctrl+A, Ctrl+C）
   - 粘贴到Tampermonkey编辑器中（Ctrl+V）

4. **保存脚本**
   - 按`Ctrl+S`保存脚本
   - 确认脚本状态为"已启用"

### 第三步：访问抖店飞鸽

1. 打开浏览器，访问：`https://im.jinritemai.com/pc_seller_v2/main/workspace`
2. 登录您的抖店账号
3. 等待页面完全加载
4. 页面底部应该出现"AI配置"按钮

## 基础配置

### 1. 打开配置面板
- 点击页面底部的"AI配置"按钮
- 配置面板会在页面中央弹出

### 2. 配置分类组
1. **查看默认分类组**
   - 脚本预设了多个分类组：通用、五代耳机、头戴式耳机等
   - 点击分类组按钮可以切换到对应分类

2. **编辑分类组**
   - 右键点击分类组按钮
   - 修改分类组名称、关键词、排序值
   - 点击"确认"保存

3. **新增分类组**
   - 点击"+新增分类组"按钮
   - 填写分类组信息
   - 点击"确认"创建

4. **删除分类组**
   - 点击分类组右上角的"×"按钮
   - 确认删除（注意：通用分类组不能删除）

### 3. 配置关键词回复
1. **添加关键词**
   - 选择要配置的分类组
   - 点击"添加关键词"按钮
   - 填写触发关键词和回复内容

2. **设置回复内容**
   - **纯文本回复**：直接在回复框中输入文字
   - **图片回复**：点击"添加图片"，选择图片文件
   - **视频回复**：点击"添加视频"，选择视频文件
   - **混合回复**：可以同时包含文字、图片、视频

3. **使用特殊触发词**
   在回复内容中可以使用以下特殊触发词：
   - `[订单商品规格]` - 自动点击第1个订单发送按钮
   - `[核对收货信息]` - 自动点击第2个订单发送按钮
   - `[承诺发货时间]` - 自动点击第3个订单发送按钮

### 4. 配置默认回复
- 在"默认回复"框中设置当没有匹配关键词时的回复内容
- 建议设置为通用的客服话术

## AI智能回复配置

### 1. 获取AI接口信息
您需要准备以下信息：
- **API地址**：AI服务的接口地址
- **应用ID**：AI应用的唯一标识符
- **授权密钥**：API访问的授权令牌

### 2. 配置AI接口
1. 在配置面板中找到"AI接口配置"部分
2. 填写API地址、应用ID、授权密钥
3. 勾选"启用AI智能回复"
4. 可选择是否"发送触发词给用户"

### 3. AI回复工作原理
- 当用户消息无法匹配任何关键词时，脚本会：
  1. 收集用户的聊天记录
  2. 获取用户的订单信息
  3. 构建包含上下文的提示词
  4. 调用AI接口获取智能回复
  5. 自动发送AI生成的回复

## 高级功能

### 1. 配置导出导入

#### 导出配置
1. 在配置面板底部点击"导出配置"
2. 浏览器会自动下载配置文件（JSON格式）
3. 建议定期导出配置作为备份

#### 导入配置
1. 点击"导入配置"按钮
2. 选择之前导出的配置文件
3. 确认导入，配置会立即生效

### 2. 媒体文件管理
- 脚本使用IndexedDB存储图片和视频
- 支持的格式：JPG、PNG、GIF、MP4、AVI等
- 建议文件大小：图片<500KB，视频<2MB

### 3. 拖拽排序
- 可以拖拽分类组按钮调整顺序
- 排序会自动保存

## 使用技巧

### 1. 关键词设置技巧
- **精确匹配**：设置具体的关键词，如"发货时间"
- **模糊匹配**：设置通用关键词，如"发货"、"物流"
- **多关键词**：一个回复可以设置多个触发关键词
- **分类管理**：按产品类型分组管理关键词

### 2. 回复内容优化
- **个性化**：根据产品特点定制回复内容
- **专业性**：使用专业的客服话术
- **完整性**：提供完整的解决方案
- **友好性**：保持友好的服务态度

### 3. AI回复优化
- **准确的产品信息**：确保订单信息准确
- **清晰的问题描述**：AI能更好理解用户需求
- **合适的提示词**：可以自定义AI的回复风格

## 监控和调试

### 1. 查看运行状态
- 页面右上角会显示脚本运行状态
- 绿色：正常运行
- 蓝色：处理中
- 橙色：警告
- 红色：错误

### 2. 控制台日志
1. 按F12打开开发者工具
2. 切换到"Console"标签
3. 查看以"[AI监控]"开头的日志信息
4. 根据日志信息排查问题

### 3. 常见状态说明
- "聊天列表容器已找到" - 脚本成功初始化
- "订单信息已更新" - 成功获取用户订单
- "聊天状态已更新" - 检测到新的用户消息
- "自动回复已发送" - 成功发送自动回复

## 注意事项

### 1. 使用环境
- 确保网络连接稳定
- 建议使用最新版本的浏览器
- 不要同时开启多个抖店飞鸽页面

### 2. 权限要求
- 需要抖店客服系统的访问权限
- 确保账号有发送消息的权限
- AI接口需要有效的授权密钥

### 3. 性能考虑
- 避免设置过多的关键词规则
- 定期清理不使用的媒体文件
- 建议在业务低峰期进行配置调整

### 4. 数据安全
- 定期备份配置数据
- 不要在公共电脑上保存敏感信息
- 注意保护客户隐私

## 故障排除

### 问题1：脚本无法启动
**现象**：页面没有出现"AI配置"按钮
**解决方案**：
1. 检查Tampermonkey是否正确安装
2. 确认脚本已保存并启用
3. 刷新抖店飞鸽页面
4. 检查浏览器控制台错误信息

### 问题2：无法自动回复
**现象**：有用户消息但不自动回复
**解决方案**：
1. 检查用户是否处于"待回复"状态
2. 确认关键词配置是否正确
3. 检查AI接口配置
4. 查看控制台日志信息

### 问题3：媒体文件发送失败
**现象**：图片或视频无法发送
**解决方案**：
1. 检查文件大小是否过大
2. 确认文件格式是否支持
3. 清除浏览器缓存
4. 重新上传媒体文件

### 问题4：AI接口调用失败
**现象**：AI智能回复不工作
**解决方案**：
1. 验证API配置信息是否正确
2. 检查网络连接
3. 确认AI服务是否正常
4. 查看API错误响应

## 技术支持

如果遇到其他问题，可以：
1. 查看项目README文档
2. 检查浏览器控制台日志
3. 尝试重新安装脚本
4. 联系技术支持

---

**提示**：建议在正式使用前，先在测试环境中验证所有配置，确保脚本能够正常工作。
